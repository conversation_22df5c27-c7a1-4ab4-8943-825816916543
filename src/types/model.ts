export interface User {
  id: string;
  name: string;
  email: string;
  avatar: string;
  gender: number;
  mobile: string;
  status: number;
  created_at: Date;
  updated_at: Date;
}

// 订单接口
export interface Order {
  id: string;
  userId: string;
  totalAmount: number;
  status: 'pending' | 'shipped' | 'delivered';
  createdAt: Date;
}

// 组合，相当于User的结构添加了一项orders
export interface UserOrders extends User {
  orders: Order[];
}

// 组合用户和订单的接口
export interface UserWithOrders {
  user: User;
  orders: Order[];
}

// 如果需要一个包含多个用户及其订单的接口
export interface UsersWithOrders {
  users: UserWithOrders[];
}

// Omit<Type, Keys>
// 从类型 Type 中删除指定的 Keys，创建一个新类型
export type UserCreate = Omit<User, 'id' | 'createdAt'>;

// Partial<Type>
// 将 Type 中的所有属性变为可选
export type UserUpdate = Partial<User>;
// 等价于：
// type UserUpdate = {
//   id?: number;
//   name?: string;
//   email?: string;
//   age?: number;
//   isAdmin?: boolean;
// }

// 组合使用 Omit 和 Partial
// 创建一个不包含 'id' 且所有字段可选的用户更新类型
export type FlexibleUserUpdate = Partial<Omit<User, 'id'>>;

