import { addSortKey } from '@/components/Table/DragTable';
import { handleParseStateChange } from '@/utils/helper';
import { Form } from 'antd';
import { produce } from 'immer';
import { debounce } from 'lodash';
import { useEffect, useState } from 'react';

const initData = {
  tableData: [],
  formData: {},
};
export const useDataSource = (currentData: { [key: string]: any }) => {
  const [dataSource, setDataSource] = useState<{ [key: string]: any }>(initData);

  const [formRef] = Form.useForm();
  // 初始化数据
  const loadInitData = async () => {
    const httpRequestData = { ...initData };
    // http获取基础数据
    if (0 === dataSource.tableData.length) {
      // 清洗数据格式化
      if (0 === httpRequestData.tableData.length) {
        httpRequestData.tableData = [
          {
            name: currentData?.name ?? undefined,
            age: 0,
            email: currentData?.email ?? undefined,
          },
        ];
      }

      httpRequestData.tableData = addSortKey(httpRequestData.tableData);
    }

    // 传递到组件
    const formData = {
      name: currentData.name,
      email: currentData.email,
    };

    formRef.setFieldsValue({ ...formData });
    setDataSource(
      produce((draft) => {
        // 改变tableData
        draft.tableData = httpRequestData.tableData;
        // 改变formData
        draft.formData = formData;
      }),
    );

    // 操作更新
    // 提交数据
    // 清洗数据
    // 调用api

    // todo... 需要初始化的在这里做
  };

  /**
   * 加载基础数据
   */
  useEffect(() => {
    loadInitData().catch(console.log);
    return () => {
      console.log('logout');
    };
  }, []);

  const handleFinish = async () => {
    console.log('handleFinish', dataSource);
  };

  /**
   * 全局状态控制
   * 加上防抖
   */
  const handleFullValueChange: any = debounce((path: string, value: any) => {
    console.log('path-value', path, value);
    const newData = handleParseStateChange(dataSource, path, value);
    setDataSource(newData);
  }, 300);

  return {
    currentData,
    formRef,
    dataSource,
    handleFinish,
    handleFullValueChange,
  };
};
