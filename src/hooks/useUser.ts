import { createUser, deleteUser, getUserList, updateUser } from '@/services/ant-design-pro/user';
import type { ActionType } from '@ant-design/pro-components';
import { Form, FormInstance, message } from 'antd';
import { produce } from 'immer';
import { debounce } from 'lodash';
import { useEffect, useRef, useState } from 'react';

const initFormData = {
  name: '',
  email: '',
  password: '123456',
  password_confirmation: '123456',
  mobile: '',
  gender: '',
  status: 1,
};

export const useUser = () => {
  const actionRef = useRef<ActionType>();
  const [modalOpen, setModalOpen] = useState<boolean>(false);
  const [dataSourceModelOpen, setDataSourceModelOpen] = useState<boolean>(false);
  const [userData, setUserData] = useState<{ [key: string]: any }>({
    formData: initFormData,
    type: '',
  });
  const [optionsData, setOptionsData] = useState({
    genderOptions: [],
    statusOptions: [],
  });

  const [formRef] = Form.useForm();

  const tableFormRef = useRef<FormInstance>();

  const [currentRecord, setCurrentRecord] = useState<{ [key: string]: any }>({});

  const genderOptions = [
    { label: 'Male', value: 1 },
    { label: 'Female', value: 2 },
  ];

  const statusOptions = [
    { label: 'Enable', value: 1 },
    { label: 'Disable', value: 0 },
  ];

  // 初始化数据
  const loadInitData = async () => {
    // http获取基础数据
    setOptionsData({
      genderOptions,
      statusOptions,
    });
    // 清洗数据格式化
    // 传递到组件
    // 操作更新
    // 提交数据
    // 清洗数据
    // 调用api

    // todo... 需要初始化的在这里做
  };

  /**
   * 加载基础数据
   */
  useEffect(() => {
    loadInitData().catch(console.log);
    return () => {
      console.log('logout');
    };
  }, []);

  /**
   * 监控高阶搜索栏变化
   * 加上防抖
   */
  const handleTableFormOnValueChange = debounce((changedValues, allValues) => {
    const changedField = Object.keys(changedValues)[0];
    const newValue = changedValues[changedField];
    console.log('handleTableFormOnValueChange:', changedField, newValue, allValues);
    // todo 联动其他表单
    // tableFormRef.current?.setFieldsValue({
    //   selectC: undefined,
    // });
  }, 300);

  const handleDateSourceModalOpen = (open: boolean = true, record: any = {}) => {
    setDataSourceModelOpen(open);
    setCurrentRecord(record);
  };

  /**
   * 打开模态框并初始化表单数据
   * @param type
   * @param record
   */
  const handleModalOpen = (type: string, record?: { [key: string]: any }) => {
    setModalOpen(true);
    setUserData(
      produce((draft) => {
        draft.type = type;
      }),
    );
    if (type === 'create') {
      formRef.setFieldsValue(userData.formData);
      return;
    }

    const upData = {
      ...record,
      date_string: '2024-05-01 21:22:12',
    };

    formRef.setFieldsValue(upData);
  };

  /**
   * 关闭模态框
   * @param isReload
   */
  const handleModalClose = (isReload = false) => {
    setModalOpen(false);
    if (isReload) {
      actionRef.current?.reload();
    }
  };

  /**
   * 操作编辑监听
   * @param changedValues
   * @param allValues
   */
  const handleValueChange = (changedValues: any, allValues: any) => {
    // userData, setUserData
    console.log(changedValues, allValues);
  };

  /**
   * 获取数据列表
   * @param params
   */
  const fetchUserList = async (params: any) => {
    const requestParams = {
      ...params,
      page: params?.current ?? 1,
    };

    const hide = message.loading('loading...');
    try {
      const userList = await getUserList(requestParams);
      if (!userList.ok) {
        message.error(userList.message);
        return [];
      }

      return userList.data;
    } catch (error) {
      message.error((error as Error).message);
      return [];
    } finally {
      hide();
    }
  };

  /**
   * 创建或修改数据
   */
  const handleUpdateOrCreate = async () => {
    const submitData = formRef.getFieldsValue();
    const hide = message.loading('loading...');
    try {
      const result =
        submitData?.id && submitData.id > 0
          ? await updateUser(submitData.id, submitData)
          : await createUser(submitData);
      if (!result.ok) {
        message.error(result.message);
        return;
      }

      message.success('Submit successfully.');
      formRef.setFieldsValue({});
      handleModalClose(true);
    } catch (error) {
      message.error((error as Error).message);
    } finally {
      hide();
    }
  };

  /**
   * 删除记录
   * @param record
   */
  const handleDelete = async (record: { [key: string]: any }) => {
    if (!record?.id || record.id <= 0) {
      message.error('ID not find.');
      return;
    }

    const hide = message.loading('loading...');
    const { id } = record;
    try {
      const result = await deleteUser(id);
      if (!result.ok) {
        message.error(result.message);
        return;
      }

      message.success('Delete successfully.');
    } catch (error) {
      message.error((error as Error).message);
    } finally {
      hide();
    }
    // 删除 record.id
    actionRef.current?.reload();
  };

  return {
    formRef,
    userData,
    modalOpen,
    actionRef,
    optionsData,
    dataSourceModelOpen,
    currentRecord,
    tableFormRef,
    fetchUserList,
    handleModalOpen,
    handleModalClose,
    handleUpdateOrCreate,
    handleDelete,
    handleValueChange,
    handleDateSourceModalOpen,
    handleTableFormOnValueChange,
  };
};
