import React from 'react';
import { PageContainer } from '@ant-design/pro-components';
import { SearchDemo } from '@/components/DebouncedSearch/demo';

const SearchDemoPage: React.FC = () => {
  return (
    <PageContainer
      title="防抖搜索组件"
      subTitle="智能搜索组件演示，支持防抖、关键词提示、异步数据获取等功能"
      breadcrumb={{
        items: [
          { title: '首页', path: '/' },
          { title: '组件演示', path: '/demo' },
          { title: '防抖搜索' }
        ]
      }}
    >
      <SearchDemo />
    </PageContainer>
  );
};

export default SearchDemoPage;
