import React from 'react';
import { PageContainer } from '@ant-design/pro-components';
import { Card, Button, Space, Typography } from 'antd';

const { Title, Paragraph } = Typography;

const MyNewPage: React.FC = () => {
  return (
    <PageContainer
      title="我的新页面"
      subTitle="这是一个新添加的页面示例"
      breadcrumb={{
        items: [
          { title: '首页', path: '/' },
          { title: '我的新页面' }
        ]
      }}
    >
      <Card>
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <Title level={2}>欢迎来到新页面！</Title>
          
          <Paragraph>
            这是一个新创建的页面示例。您可以在这里添加任何您需要的内容。
          </Paragraph>

          <Space>
            <Button type="primary">主要按钮</Button>
            <Button>普通按钮</Button>
            <Button type="dashed">虚线按钮</Button>
          </Space>

          <div style={{ 
            padding: '20px', 
            backgroundColor: '#f5f5f5', 
            borderRadius: '8px' 
          }}>
            <Title level={4}>页面内容区域</Title>
            <Paragraph>
              您可以在这里添加表格、表单、图表或其他任何组件。
            </Paragraph>
          </div>
        </Space>
      </Card>
    </PageContainer>
  );
};

export default MyNewPage;
