import React, { useEffect, useState } from 'react';
import { Card, Button, Table, message, Space, Divider } from 'antd';
import { PageContainer } from '@ant-design/pro-components';
import { currentUser, getUserList, rule, getNotices } from '@/services/ant-design-pro/api';
import { getUserList as getUserListFromUser } from '@/services/ant-design-pro/user';

const MockTest: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [currentUserData, setCurrentUserData] = useState<any>(null);
  const [users, setUsers] = useState<any[]>([]);
  const [rules, setRules] = useState<any[]>([]);
  const [notices, setNotices] = useState<any[]>([]);

  // 测试获取当前用户
  const testCurrentUser = async () => {
    setLoading(true);
    try {
      const response = await currentUser();
      console.log('当前用户响应:', response);
      setCurrentUserData(response.data);
      message.success('获取当前用户成功');
    } catch (error) {
      console.error('获取当前用户失败:', error);
      message.error('获取当前用户失败');
    } finally {
      setLoading(false);
    }
  };

  // 测试获取用户列表
  const testUserList = async () => {
    setLoading(true);
    try {
      const response = await getUserListFromUser({ current: 1, pageSize: 10 });
      console.log('用户列表响应:', response);
      setUsers(response.data?.data || []);
      message.success('获取用户列表成功');
    } catch (error) {
      console.error('获取用户列表失败:', error);
      message.error('获取用户列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 测试获取规则列表
  const testRuleList = async () => {
    setLoading(true);
    try {
      const response = await rule({ current: 1, pageSize: 10 });
      console.log('规则列表响应:', response);
      setRules(response.data?.data || []);
      message.success('获取规则列表成功');
    } catch (error) {
      console.error('获取规则列表失败:', error);
      message.error('获取规则列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 测试获取通知列表
  const testNoticeList = async () => {
    setLoading(true);
    try {
      const response = await getNotices();
      console.log('通知列表响应:', response);
      setNotices(response.data?.data || []);
      message.success('获取通知列表成功');
    } catch (error) {
      console.error('获取通知列表失败:', error);
      message.error('获取通知列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 页面加载时自动测试
  useEffect(() => {
    testCurrentUser();
    testUserList();
    testRuleList();
    testNoticeList();
  }, []);

  const userColumns = [
    { title: 'ID', dataIndex: 'id', key: 'id' },
    { title: '姓名', dataIndex: 'name', key: 'name' },
    { title: '邮箱', dataIndex: 'email', key: 'email' },
    { title: '状态', dataIndex: 'status', key: 'status' },
    { title: '角色', dataIndex: 'role', key: 'role' },
  ];

  const ruleColumns = [
    { title: 'Key', dataIndex: 'key', key: 'key' },
    { title: '名称', dataIndex: 'name', key: 'name' },
    { title: '负责人', dataIndex: 'owner', key: 'owner' },
    { title: '状态', dataIndex: 'status', key: 'status' },
    { title: '进度', dataIndex: 'progress', key: 'progress' },
  ];

  const noticeColumns = [
    { title: 'ID', dataIndex: 'id', key: 'id' },
    { title: '标题', dataIndex: 'title', key: 'title' },
    { title: '类型', dataIndex: 'type', key: 'type' },
    { title: '时间', dataIndex: 'datetime', key: 'datetime' },
    { title: '已读', dataIndex: 'read', key: 'read', render: (read: boolean) => read ? '是' : '否' },
  ];

  return (
    <PageContainer
      title="Mock数据测试页面"
      subTitle="测试本地模拟数据是否正常工作"
    >
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        <Card title="测试操作">
          <Space>
            <Button onClick={testCurrentUser} loading={loading}>
              测试当前用户
            </Button>
            <Button onClick={testUserList} loading={loading}>
              测试用户列表
            </Button>
            <Button onClick={testRuleList} loading={loading}>
              测试规则列表
            </Button>
            <Button onClick={testNoticeList} loading={loading}>
              测试通知列表
            </Button>
          </Space>
        </Card>

        {currentUserData && (
          <Card title="当前用户信息">
            <pre>{JSON.stringify(currentUserData, null, 2)}</pre>
          </Card>
        )}

        <Card title="用户列表">
          <Table
            dataSource={users}
            columns={userColumns}
            rowKey="id"
            pagination={false}
            size="small"
          />
        </Card>

        <Card title="规则列表">
          <Table
            dataSource={rules}
            columns={ruleColumns}
            rowKey="key"
            pagination={false}
            size="small"
          />
        </Card>

        <Card title="通知列表">
          <Table
            dataSource={notices}
            columns={noticeColumns}
            rowKey="id"
            pagination={false}
            size="small"
          />
        </Card>
      </Space>
    </PageContainer>
  );
};

export default MockTest;
