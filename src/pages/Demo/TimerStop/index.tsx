import BaseContainer, {ModalType} from '@/components/Container';
import UploadFiles from '@/components/UploadFiles';
import {useTimerStop} from '@/hooks/useTimerStop';
import {useProduct} from '@/hooks/useProduct';
import CardList from '@/pages/Demo/TimerStop/components/CardList';
import {Button} from 'antd';
import React, {useState} from 'react';
import {produce} from 'immer';

const TimerStop: React.FC = () => {
  const {
    cnt,
    dataSource,
    containerOpen,
    handleFullValueChange,
    handleContainerOpenOrClose,
    handleFileChange
  } = useTimerStop();

  const {totalCount, handleAddCount} = useProduct(cnt);
  const [totalCount2, handleAddCount2] = useState({data: 0});

  return (
    <>
      <Button
        key="totalCount2"
        onClick={() => {
          handleAddCount2(
            produce((draft) => {
              draft.data += 1;
            }),
          );
          //
        }}
      >
        {totalCount2.data}
      </Button>
      {/*// {}*/}
      <UploadFiles
        value={dataSource?.fileIds ?? ''}
        onChange={handleFileChange}
        fileLength={10}
        showDownloadIcon={true}
      />
      <Button key="totalCount" onClick={handleAddCount}>A{totalCount}A</Button>
      <div>{cnt}</div>
      <Button key="handleContainerOpenOrClose" className="" onClick={() => handleContainerOpenOrClose(true)}>
        暂停更新
      </Button>
      <BaseContainer
        type={ModalType.Modal}
        title="test"
        // style={{top: 30}}
        width="75%"
        open={containerOpen}
        maskClosable={false}
        onCancel={() => handleContainerOpenOrClose(false)}
        // afterOpenChange={() => console.log('hello')}
        // afterClose={() => console.log('closed')}
      >
        <CardList timeList={dataSource.cardData} onFullValueChange={handleFullValueChange}/>
      </BaseContainer>
    </>
  );
};

export default TimerStop;
