import React from 'react';
import CardItem from "@/pages/Demo/TimerStop/components/CardItem";


export type CardListProps = {
  timeList: {[key: string]: any}
  onFullValueChange: (path: string, value: any, isPause?: boolean) => void
};



const CardList: React.FC<CardListProps> = ({timeList, onFullValueChange }) => {

  return (
    timeList && timeList.data && timeList.data.map((item, key) => {
      return (
        <CardItem key={key} data={item} index={key} onValueChange={onFullValueChange} />
      )
    })
)
};

export default CardList;
