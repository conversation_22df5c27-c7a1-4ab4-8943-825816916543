import React from 'react';
import {Card, TimePicker} from 'antd';
import {t} from "@/utils/helper";


// TimePicker.RangePicker
export type CardItemProps = {
  index: number
  data: {[key: string]: string}
  onValueChange: (path: string, value: any, isPause?: boolean) => void
};
const CardItem: React.FC<CardItemProps> = ({index, data, onValueChange}) => {
  const path = `cardData:data:${index}`
  return (
    <Card title="hello" style={{ width: '100%' }}>
      <div>{t('navBar.lang')} - {index}</div>
      <div style={{ display: 'flex', justifyContent: 'space-between', flexWrap: 'wrap', gap: '16px' }}>
        <TimePicker.RangePicker
          style={{ width: 'calc(50% - 8px)' }}
          onChange={(times, a, b, c) => {
            console.log("onChange:", times, a, b, c)
            onValueChange(path, times)
          }}
          onOpenChange={(isBegin) => {
            console.log("onOpen:", isBegin)
            onValueChange(path, null, isBegin);
          }}
          format="HH:mm:ss"
          minuteStep={1}
          hourStep={1}
          secondStep={30}
          showSecond={false}
          value={data}
        />
      </div>
    </Card>
  )
};

export default CardItem;
