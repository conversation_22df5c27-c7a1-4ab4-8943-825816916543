import React, { useState, useEffect } from 'react';
import { Form, Input, Button, Space, message } from 'antd';
import BaseFormList from "../../../components/FormList";
import {MinusCircleOutlined, PlusCircleOutlined} from "@ant-design/icons";

const TaskListDemo = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const baseStruct = {
    item: '',
    fileIds: 0
  }

  useEffect(() => {
    const initialData = {
      tasks: [
        { name: '完成报告', description: '周五之前完成季度报告' },
        { name: '召开会议', description: '与团队讨论新项目' },
      ],
    };

    try {
      form.setFieldsValue(initialData);
    } catch (error) {
      console.error('设置初始数据时出错:', error);
      message.error('初始化表单数据失败');
    }
  }, [form]);

  const handleClick = (index) => {
    console.log(index, form.getFieldsValue())
  }

  const onFinish = (values) => {
    setLoading(true);
    try {
      console.log('提交的数据:', values);
      // 这里可以添加 API 调用来保存数据
      message.success('提交成功');
    } catch (error) {
      console.error('提交数据时出错:', error);
      message.error('提交失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  const FormListItem: React.FC<any> = ({restField, name, remove, add, index}) => {
    return (
      <Space key={restField.key} style={{display: 'flex', marginBottom: 8}} align="baseline">
        <Form.Item
          {...restField}
          key={`formInput-${restField.key}`}
          style={index !== 0 ? {marginLeft: 65} : {}}
          name={[name, 'item']}
          label={index === 0 ? 'ListItem' : ''}
        >
          <Input/>
        </Form.Item>
        <MinusCircleOutlined onClick={() => remove(index)}/>
        <PlusCircleOutlined onClick={() => add(baseStruct, index + 1)}/>
        <Button onClick={() => handleClick(index)}>save</Button>
      </Space>
    )
  }
  // const AddTaskButton = () => (
  //   <Button type="dashed" block>
  //     添加任务
  //   </Button>
  // );

  return (
    <Form form={form} onFinish={onFinish}>
      <BaseFormList
        key="list"
        listName="list"
        addButton={<Button type="dashed">Add Row</Button>}
        addStruct={baseStruct}
        disableRemoveLast={false}
      >
        <FormListItem key="list-litem1"/>
      </BaseFormList>
      <Form.Item>
        <Button type="primary" htmlType="submit" loading={loading}>
          提交
        </Button>
      </Form.Item>
    </Form>
  );
};

export default TaskListDemo;
