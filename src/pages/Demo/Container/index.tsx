import React from 'react';
import TableTest1 from "@/pages/Demo/Container/TableTest1";
import {isEmpty} from "lodash";
import SubmitButton from "@/components/Buttons/SubmitButton";
import {Button} from "antd";
import BaseContainer, {ModalType} from "@/components/Container";
import TableTest2 from "@/pages/Demo/Container/TableTest2";
import FormTest from "@/pages/Demo/Container/FormTest";
import {useDemoContainer} from "@/hooks/useDemoContainer";



const Index: React.FC = () => {
  const {
    dataSource,
    containerOpen,
    options,
    handleValueChange,
    handleOpen,
    handleClose,
    handleSubmit
  } = useDemoContainer();
  return (
    <>
      <Button type="primary" onClick={handleOpen}>打开</Button>
      <BaseContainer
        type={ModalType.Drawer}
        title="test"
        style={{top: 30}}
        width="75%"
        open={containerOpen}
        onCancel={() => handleClose()}
        afterOpenChange={() => console.log('hello')}
        afterClose={() => console.log('closed')}
      >
        <TableTest1
          key="tableTest1"
          onValueChange={handleValueChange}
          sourceKey="tableTest1"
          dataSource={isEmpty(dataSource?.tableTest1) ? [] : dataSource.tableTest1}
        />
        <FormTest
          key="formData"
          sourceKey="formData"
          dataSource={isEmpty(dataSource?.formData) ? {} : dataSource.formData}
          onValueChange={handleValueChange}
          options={options}
        />
        <TableTest2
          key="tableTest2"
          onValueChange={handleValueChange}
          sourceKey="tableTest2"
          dataSource={isEmpty(dataSource?.tableTest2) ? [] : dataSource.tableTest2}
        />
        <SubmitButton style={{marginRight: 15}} type="primary" onConfirm={handleSubmit}>
          提交
        </SubmitButton>
        <Button onClick={() => handleClose()}>取消</Button>
      </BaseContainer>
    </>
  );
};

export default Index;
