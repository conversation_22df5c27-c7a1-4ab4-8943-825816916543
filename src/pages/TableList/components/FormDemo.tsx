import React from 'react';
import {Button, Form, FormInstance, Input, Space} from "antd";
import {MinusCircleOutlined, PlusCircleOutlined} from "@ant-design/icons";
import BaseFormList from "@/components/FormList";
import UploadFiles from "@/components/UploadFiles";


interface FormDemoProps {
  formRef: FormInstance;
  sourceKey: string;
  onValueChange: (path: string, value: any) => void;
}

const baseStruct = {
  item: '',
  // fileIds: 0
}


const FormListItem: React.FC<any> = ({
                                       restField,
                                       name,
                                       remove,
                                       add,
                                       index
                                     }) => {
  return (
    <Space key={restField.key} style={{display: 'flex', marginBottom: 8}} align="baseline">
      <Form.Item
        {...restField}
        key={`formInput-${restField.key}`}
        style={index !== 0 ? {marginLeft: 65} : {}}
        name={[name, 'item']}
        label={index === 0 ? 'ListItem' : ''}
      >
        <Input/>
      </Form.Item>
      <MinusCircleOutlined onClick={() => remove(index)}/>
      <PlusCircleOutlined onClick={() => add(baseStruct, index + 1)}/>
    </Space>
  )
}

const FormDemo: React.FC<FormDemoProps> = ({formRef, sourceKey, onValueChange}) => {
  return (
    <Form
      form={formRef}
      onValuesChange={(changedValues, value) => onValueChange(`${sourceKey}`, value)}
      initialValues={
        {
          time_ranges: [{date: ['', '']}]
        }
      }
    >
      <Form.Item label="Name" name="name" wrapperCol={{span: 12}} labelCol={{span: 2}} labelAlign="right">
        <Input/>
      </Form.Item>
      <Form.Item label="Email" name="email" wrapperCol={{span: 12}} labelCol={{span: 2}} labelAlign="right">
        <Input/>
      </Form.Item>
      <Form.Item label="File Upload" name="uploadFileIds" wrapperCol={{span: 12}} labelCol={{span: 4}} labelAlign="right">
        <UploadFiles fileLength={20} />
      </Form.Item>
      <BaseFormList
        key="list_item"
        // listName={['list', 'list_item']}
        listName="list"
        addButton={<Button type="dashed">Add Row</Button>}
        addStruct={baseStruct}
        disableRemoveLast={true}
        wrapperCol={{span: 12}}
        labelCol={{span: 2}}
      >
        <FormListItem key="list-item-1"/>
      </BaseFormList>
    </Form>
  );
};

export default FormDemo;
