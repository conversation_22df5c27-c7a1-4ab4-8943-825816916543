import {Button, Space} from 'antd';
import React from 'react';
import {useDataSource} from "@/hooks/useDataSource";
import TableDemo from "@/pages/TableList/components/TableDemo";
import SubmitButton from "@/components/Buttons/SubmitButton";
import FormDemo from "@/pages/TableList/components/FormDemo";


interface TableAndFormProps {
  recordData: {[key: string]: any};
  onDateSourceModalClose: (open: boolean) => void;
}

const TableAndForm: React.FC<TableAndFormProps> = ({recordData, onDateSourceModalClose}) => {

  const {currentData, formRef, dataSource, handleFullValueChange, handleFinish} = useDataSource(recordData);

  return (
    <>
      <h3>{currentData?.name ?? ''}</h3>
      <br/>
      <TableDemo sourceKey="tableData" dataSource={dataSource.tableData} onValueChange={handleFullValueChange}/>
      <br/>
      <FormDemo  onValueChange={handleFullValueChange} sourceKey="formData" formRef={formRef}/>
      <br/>
      <Space>
        <SubmitButton style={{marginRight: 15}} type="primary" onConfirm={handleFinish}>
          提交
        </SubmitButton>
        <Button style={{marginRight: 15}} onClick={() => onDateSourceModalClose(false)}>
          取消
        </Button>
      </Space>

    </>
  );
};

export default TableAndForm;
