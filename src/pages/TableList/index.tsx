import { PlusOutlined } from '@ant-design/icons';
import type { ProColumns } from '@ant-design/pro-components';
import { PageContainer, ProTable } from '@ant-design/pro-components';
import { FormattedMessage } from '@umijs/max';
import { Button, Space, TablePaginationConfig, Tag } from 'antd';
import React from 'react';

import DeleteButton from '@/components/Buttons/DeleteButton';
import BaseContainer, { ModalType } from '@/components/Container';
import { useUser } from '@/hooks/useUser';
import TableAndForm from '@/pages/TableList/components/TableAndForm';
import UpdateOrCreateForm from '@/pages/TableList/components/UpdateOrCreateForm';
import { User } from '@/types';
import { getTableScroll, t } from '@/utils/helper';

const pageParams = {
  pageSize: 20,
} as TablePaginationConfig;

const TableList: React.FC = () => {
  const {
    formRef,
    userData,
    modalOpen,
    actionRef,
    optionsData,
    dataSourceModelOpen,
    currentRecord,
    tableFormRef,
    fetchUserList,
    handleModalOpen,
    handleModalClose,
    handleUpdateOrCreate,
    handleDelete,
    handleValueChange,
    handleDateSourceModalOpen,
    handleTableFormOnValueChange,
  } = useUser();

  const columns: ProColumns<User>[] = [
    {
      title: 'No.',
      dataIndex: 'id',
      tooltip: 'User ID',
      search: false,
    },
    {
      title: 'Name',
      dataIndex: 'name',
    },
    {
      title: 'Gender',
      dataIndex: 'gender',
      valueType: 'select',
      fieldProps: {
        options: optionsData.genderOptions,
        showSearch: true,
        filterOption: (input: string, option?: { label: string; value: number }) =>
          (option?.label ?? '').toLowerCase().includes(input.toLowerCase()),
      },
      renderText: (dom, entity) => {
        return 1 === parseInt(entity.gender.toString()) ? 'Male' : 'Female';
      },
    },
    {
      title: 'Mobile',
      dataIndex: 'mobile',
    },
    {
      title: 'Email',
      dataIndex: 'email',
    },
    {
      title: 'Status',
      dataIndex: 'status',
      // 修改这里，添加搜索功能
      search: true,
      valueType: 'select',
      fieldProps: {
        options: optionsData.statusOptions,
        showSearch: true,
        filterOption: (input: string, option?: { label: string; value: number }) =>
          (option?.label ?? '').toLowerCase().includes(input.toLowerCase()),
        mode: 'multiple',
      },
      render: (dom, entity) => {
        return 1 === entity.status ? (
          <>
            <Tag color="green">enable</Tag>
          </>
        ) : (
          <Tag color="red">disable</Tag>
        );
      },
    },
    {
      title: 'Created At',
      dataIndex: 'created_at',
      search: false,
      renderText: (dom, entity) => {
        return entity.created_at;
      },
    },
    {
      title: 'Action',
      valueType: 'option',
      key: 'option',
      fixed: 'right',
      width: 120,
      render: (_, record) => (
        <Space size="small">
          <Button type="link" size="small" onClick={() => handleModalOpen('Edit', record)}>
            Edit
          </Button>
          <DeleteButton type="link" size="small" danger onConfirm={() => handleDelete(record)}>
            Delete
          </DeleteButton>
          <Button type="link" size="small" onClick={() => handleDateSourceModalOpen(true, record)}>
            DateSource
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <PageContainer>
      <ProTable
        headerTitle={t('pages.searchTable.title', 'Enquiry form')}
        actionRef={actionRef}
        rowKey="id"
        search={{
          labelWidth: 120,
        }}
        toolBarRender={() => [
          <Button type="primary" key="primary" onClick={() => handleModalOpen('create')}>
            <PlusOutlined /> <FormattedMessage id="pages.searchTable.new" defaultMessage="New" />
          </Button>,
        ]}
        scroll={{
          x: 'max-content', // 确保水平滚动条在可视区域
          y: getTableScroll(window), // 设置表格高度，使垂直滚动条在可视区域
          scrollToFirstRowOnChange: true,
        }}
        // sticky={{ // 固定表头
        //   offsetHeader: 64, // PageContainer 的高度
        // }}
        request={fetchUserList}
        columns={columns}
        pagination={pageParams}
        rowSelection={{
          onChange: (index, selectedRows) => {
            console.log(index, selectedRows);

            // setSelectedRows(selectedRows);
          },
        }}
        // 高阶搜索栏变动
        formRef={tableFormRef}
        form={{
          onValuesChange: handleTableFormOnValueChange,
        }}
      />
      <BaseContainer
        type={ModalType.Modal}
        title={userData.type.toUpperCase() + ' USER'}
        // style={{top: 30}}
        width="35%"
        open={modalOpen}
        maskClosable={false}
        onCancel={() => handleModalClose()}
        // afterOpenChange={() => console.log('hello')}
        // afterClose={() => console.log('closed')}
      >
        <UpdateOrCreateForm
          formRef={formRef}
          formType={userData.type}
          onFinish={handleUpdateOrCreate}
          onChange={handleValueChange}
        />
      </BaseContainer>
      <BaseContainer
        type={ModalType.Drawer}
        title="DataSource 示例"
        // style={{top: 30}}
        width="60%"
        open={dataSourceModelOpen}
        maskClosable={false}
        onCancel={() => handleDateSourceModalOpen(false)}
      >
        <TableAndForm
          recordData={currentRecord}
          onDateSourceModalClose={handleDateSourceModalOpen}
        />
      </BaseContainer>
    </PageContainer>
  );
};

export default TableList;
