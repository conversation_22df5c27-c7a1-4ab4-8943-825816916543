import {request} from "@@/exports";
import {ResponseStructure} from "@/utils/http-handle";
import { requestConfig } from '@/config/request-config';
import { mockManager } from '@/mock/mockManager';

export async function getUserList(params?: { [key: string]: any }, options?: { [key: string]: any }) {
  console.log("ppp", params)
  if (requestConfig.useMock) {
    return mockManager.getUserService().getUserList(params);
  }
  return request<ResponseStructure>('/api/backend/v1/users', {
    method: 'GET',
    params: {
      ...params
    },
    ...(options || {}),
  });
}

export async function createUser(params: { [key: string]: any }, options?: { [key: string]: any }) {
  if (requestConfig.useMock) {
    return mockManager.getUserService().createUser(params);
  }
  return request<ResponseStructure>('/api/backend/v1/create-user', {
    method: 'POST',
    data: {
      ...params
    },
    ...(options || {}),
  });
}

export async function updateUser(id: number, params: { [key: string]: any }, options?: { [key: string]: any }) {
  return request<ResponseStructure>(`/api/backend/v1/update-user/${id}`, {
    method: 'PUT',
    data: {
      ...params
    },
    ...(options || {}),
  });
}

export async function deleteUser(id: number, options?: { [key: string]: any }) {
  return request<ResponseStructure>(`/api/backend/v1/delete-user/${id}`, {
    method: 'DELETE',
    ...(options || {}),
  });
}
