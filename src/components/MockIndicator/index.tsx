import React from 'react';
import { Alert, Switch, Space } from 'antd';
import { mockConfig, setMockEnabled, isMockEnabled } from '@/config/mock-config';
import './index.less';

const MockIndicator: React.FC = () => {
  const [enabled, setEnabled] = React.useState(isMockEnabled());

  const handleToggle = (checked: boolean) => {
    setMockEnabled(checked);
    setEnabled(checked);
    // 提示用户刷新页面以应用更改
    if (checked !== isMockEnabled()) {
      window.location.reload();
    }
  };

  if (!enabled) {
    return null;
  }

  return (
    <div className="mock-indicator">
      <Alert
        message={
          <Space>
            <span>🔧 当前使用模拟数据</span>
            <Switch
              size="small"
              checked={enabled}
              onChange={handleToggle}
              checkedChildren="Mock"
              unCheckedChildren="API"
            />
          </Space>
        }
        type="info"
        showIcon
        closable
        style={{
          position: 'fixed',
          top: 10,
          right: 10,
          zIndex: 9999,
          maxWidth: 300,
        }}
      />
    </div>
  );
};

export default MockIndicator;
