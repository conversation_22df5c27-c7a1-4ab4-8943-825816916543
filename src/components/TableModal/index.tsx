import React, { useState } from 'react';
import { Button, Modal } from 'antd';
import ProTable from '@ant-design/pro-table';

/**
 * 单选表格弹窗组件
 * @param {Object} props - 组件属性
 * @param {string} props.title - 弹窗标题
 * @param {string} props.triggerText - 触发按钮文本
 * @param {Array} props.columns - 表格列配置
 * @param {Array} props.dataSource - 表格数据源
 * @param {Function} props.onSelect - 选中数据后的回调函数
 * @returns {JSX.Element} 组件
 */
const TableModalSelector = ({
                              title = '选择数据',
                              triggerText = '打开选择器',
                              columns,
                              dataSource,
                              onSelect,
                            }) => {
  // 状态管理
  const [visible, setVisible] = useState(false);
  const [selectedRow, setSelectedRow] = useState(null);

  // 打开弹窗
  const handleOpenModal = () => {
    setVisible(true);
    // 重置选中状态
    setSelectedRow(null);
  };

  // 关闭弹窗
  const handleCancel = () => {
    setVisible(false);
  };

  // 处理表格行选择
  const handleRowSelection = (selectedKeys, selectedRows) => {
    if (selectedRows.length > 0) {
      setSelectedRow(selectedRows[0]);
    } else {
      setSelectedRow(null);
    }
  };

  // 确认选择
  const handleConfirm = () => {
    if (selectedRow && onSelect) {
      onSelect(selectedRow);
      setVisible(false);
    }
  };

  return (
    <>
      {/* 触发按钮 */}
      <Button type="primary" onClick={handleOpenModal}>
        {triggerText}
      </Button>

      {/* 弹窗组件 */}
      <Modal
        title={title}
        open={visible}
        onCancel={handleCancel}
        onOk={handleConfirm}
        confirmLoading={false}
        destroyOnClose
        width={800}
      >
        {/* ProTable组件 */}
        <ProTable
          columns={columns}
          dataSource={dataSource}
          rowKey="id"
          pagination={{ pageSize: 5 }}
          rowSelection={{
            type: 'radio',
            onChange: handleRowSelection,
            selectedRowKeys: selectedRow ? [selectedRow.id] : [],
          }}
          options={{
            search: false,
            refresh: false,
            density: false,
          }}
        />
      </Modal>
    </>
  );
};

export default TableModalSelector;
