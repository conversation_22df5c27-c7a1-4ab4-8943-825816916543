# DebouncedSearch 防抖搜索组件

一个功能强大的防抖搜索组件，支持智能关键词提示、异步数据获取、多种尺寸等特性。

## 特性

- 🔍 **智能关键词提示** - 根据输入内容自动显示匹配的关键词
- ⚡ **防抖提示框显示** - 输入停顿后才显示提示框，避免频繁触发
- 🎯 **关键词高亮** - 在提示列表中高亮显示匹配的文字
- 🔄 **异步数据获取** - 支持从API动态获取关键词，防抖避免频繁请求
- 📱 **响应式设计** - 适配不同屏幕尺寸
- 🎛️ **受控/非受控** - 支持两种使用模式
- 🎨 **多种尺寸** - 提供 small、middle、large 三种尺寸
- ⌨️ **键盘导航** - 支持上下键选择、回车确认
- 🚫 **禁用状态** - 支持禁用状态
- ⏱️ **可调节防抖延迟** - 可自定义防抖延迟时间，适应不同场景

## 基础用法

```tsx
import DebouncedSearch from '@/components/DebouncedSearch';

const App = () => {
  const handleSearch = (value: string) => {
    console.log('搜索:', value);
  };

  return (
    <DebouncedSearch
      onSearch={handleSearch}
      placeholder="请输入搜索关键词"
    />
  );
};
```

## 防抖加载演示

```tsx
const App = () => {
  const handleSearch = (value: string) => {
    console.log('搜索:', value);
  };

  const handleChange = (value: string) => {
    console.log('输入变化 (防抖):', value);
  };

  return (
    <DebouncedSearch
      onSearch={handleSearch}
      onChange={handleChange}
      debounceWait={500} // 防抖延迟 500ms，输入停顿后才显示提示框
      placeholder="输入后停顿500ms才显示提示框"
    />
  );
};
```

## 不同防抖延迟对比

```tsx
// 快速响应 - 100ms
<DebouncedSearch debounceWait={100} placeholder="快速响应" />

// 标准响应 - 300ms (默认)
<DebouncedSearch debounceWait={300} placeholder="标准响应" />

// 慢速响应 - 800ms
<DebouncedSearch debounceWait={800} placeholder="慢速响应" />
```

## 异步关键词获取

```tsx
const App = () => {
  const fetchKeywords = async (query: string): Promise<string[]> => {
    const response = await fetch(`/api/keywords?q=${query}`);
    const data = await response.json();
    return data.keywords;
  };

  return (
    <DebouncedSearch
      onSearch={handleSearch}
      fetchKeywords={fetchKeywords}
      placeholder="输入内容动态获取关键词"
    />
  );
};
```

## 受控组件

```tsx
const App = () => {
  const [value, setValue] = useState('');

  return (
    <DebouncedSearch
      value={value}
      onChange={setValue}
      onSearch={handleSearch}
      placeholder="受控组件"
    />
  );
};
```

## 自定义关键词

```tsx
const customKeywords = [
  'React开发',
  'Vue.js',
  'Angular',
  'JavaScript',
  'TypeScript'
];

const App = () => {
  return (
    <DebouncedSearch
      keywords={customKeywords}
      onSearch={handleSearch}
      maxSuggestions={5}
      placeholder="自定义关键词列表"
    />
  );
};
```

## API

### Props

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| onSearch | 搜索回调函数 | `(value: string) => void` | - |
| onChange | 输入变化回调（防抖） | `(value: string) => void` | - |
| placeholder | 占位符文本 | `string` | `"请输入搜索关键词"` |
| debounceWait | 防抖延迟时间（毫秒） | `number` | `300` |
| loading | 是否显示加载状态 | `boolean` | `false` |
| keywords | 自定义关键词数据源 | `string[]` | 内置关键词 |
| enableSuggestions | 是否启用关键词提示 | `boolean` | `true` |
| maxSuggestions | 最大显示提示数量 | `number` | `8` |
| size | 组件尺寸 | `'small' \| 'middle' \| 'large'` | `'large'` |
| allowClear | 是否允许清空 | `boolean` | `true` |
| style | 自定义样式 | `React.CSSProperties` | - |
| className | 自定义类名 | `string` | - |
| defaultValue | 初始值 | `string` | `''` |
| value | 受控值 | `string` | - |
| disabled | 是否禁用 | `boolean` | `false` |
| fetchKeywords | 获取异步关键词的函数 | `(query: string) => Promise<string[]>` | - |

## 注意事项

1. **防抖机制**：提示框的显示有防抖延迟，输入停顿后才会显示，避免频繁触发
2. **异步获取**：当同时提供 `keywords` 和 `fetchKeywords` 时，优先使用 `fetchKeywords`
3. **回调触发**：`onChange` 有防抖延迟，`onSearch` 会立即触发
4. **加载状态**：异步获取关键词时会显示加载状态
5. **受控模式**：受控模式下需要同时提供 `value` 和 `onChange`
6. **内存管理**：组件会自动处理防抖函数的清理，无需手动清理
7. **性能优化**：合理设置 `debounceWait` 可以平衡响应速度和性能

## 样式定制

组件使用 Ant Design 的样式系统，可以通过以下方式定制样式：

```tsx
<DebouncedSearch
  style={{ maxWidth: 600 }}
  className="custom-search"
  onSearch={handleSearch}
/>
```

```css
.custom-search .ant-input-search {
  border-radius: 8px;
}

.custom-search .ant-input-search-button {
  background: linear-gradient(45deg, #1890ff, #36cfc9);
}
```
