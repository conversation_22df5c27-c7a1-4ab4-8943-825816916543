import React, { useState } from 'react';
import { Spin } from 'antd';
import DebouncedSearch from './index';

// 演示组件
export const SearchDemo = () => {
  const [searchResults, setSearchResults] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchValue, setSearchValue] = useState('');

  // 模拟异步获取关键词
  const fetchAsyncKeywords = async (query: string): Promise<string[]> => {
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const mockApiKeywords = [
      `${query} 教程`,
      `${query} 实战`,
      `${query} 进阶`,
      `${query} 最佳实践`,
      `${query} 性能优化`,
      `${query} 源码解析`,
      `${query} 面试题`,
      `${query} 项目实战`
    ];
    
    return mockApiKeywords.filter(keyword => 
      keyword.toLowerCase().includes(query.toLowerCase())
    );
  };

  const handleSearch = (keyword: string) => {
    console.log('搜索关键词:', keyword);
    setLoading(true);

    // 模拟搜索API调用
    setTimeout(() => {
      const mockResults = [
        `关于 "${keyword}" 的搜索结果 1 - 这是一个详细的搜索结果描述`,
        `关于 "${keyword}" 的搜索结果 2 - 包含相关的技术文档和教程`,
        `关于 "${keyword}" 的搜索结果 3 - 实际项目案例和最佳实践`,
        `关于 "${keyword}" 的搜索结果 4 - 社区讨论和问题解答`,
        `关于 "${keyword}" 的搜索结果 5 - 最新的技术动态和更新`
      ];
      setSearchResults(mockResults);
      setLoading(false);
    }, 1000);
  };

  const handleInputChange = (value: string) => {
    console.log('输入变化 (防抖):', value);
    setSearchValue(value);
  };

  return (
    <div style={{ padding: '40px', backgroundColor: '#f5f5f5', minHeight: '100vh' }}>
      <div style={{
        maxWidth: '1000px',
        margin: '0 auto',
        backgroundColor: 'white',
        borderRadius: '12px',
        padding: '40px',
        boxShadow: '0 2px 16px rgba(0, 0, 0, 0.08)'
      }}>
        <h1 style={{
          textAlign: 'center',
          marginBottom: '40px',
          color: '#262626',
          fontSize: '28px',
          fontWeight: '600'
        }}>
          防抖搜索组件演示
        </h1>

        {/* 基础用法 */}
        <div style={{ marginBottom: '40px' }}>
          <h2 style={{ marginBottom: '16px', fontSize: '18px', color: '#262626' }}>
            基础用法 - 防抖延迟 300ms
          </h2>
          <p style={{ marginBottom: '16px', color: '#8c8c8c', fontSize: '14px' }}>
            💡 输入后停顿 300ms 才会显示提示框，避免频繁触发
          </p>
          <DebouncedSearch
            onSearch={handleSearch}
            onChange={handleInputChange}
            placeholder="输入关键词，停顿后显示提示..."
            loading={loading}
            debounceWait={300}
          />
        </div>

        {/* 快速响应演示 */}
        <div style={{ marginBottom: '40px' }}>
          <h2 style={{ marginBottom: '16px', fontSize: '18px', color: '#262626' }}>
            快速响应 - 防抖延迟 100ms
          </h2>
          <p style={{ marginBottom: '16px', color: '#8c8c8c', fontSize: '14px' }}>
            ⚡ 更短的防抖延迟，响应更快但可能触发更频繁
          </p>
          <DebouncedSearch
            onSearch={handleSearch}
            onChange={handleInputChange}
            placeholder="快速响应模式..."
            loading={loading}
            debounceWait={100}
          />
        </div>

        {/* 慢速响应演示 */}
        <div style={{ marginBottom: '40px' }}>
          <h2 style={{ marginBottom: '16px', fontSize: '18px', color: '#262626' }}>
            慢速响应 - 防抖延迟 800ms
          </h2>
          <p style={{ marginBottom: '16px', color: '#8c8c8c', fontSize: '14px' }}>
            🐌 更长的防抖延迟，减少触发频率，适合复杂搜索
          </p>
          <DebouncedSearch
            onSearch={handleSearch}
            onChange={handleInputChange}
            placeholder="慢速响应模式，停顿800ms后显示..."
            loading={loading}
            debounceWait={800}
          />
        </div>

        {/* 异步关键词获取 */}
        <div style={{ marginBottom: '40px' }}>
          <h2 style={{ marginBottom: '16px', fontSize: '18px', color: '#262626' }}>
            异步关键词获取 - 防抖延迟 500ms
          </h2>
          <p style={{ marginBottom: '16px', color: '#8c8c8c', fontSize: '14px' }}>
            🔄 从API动态获取关键词，防抖避免频繁请求
          </p>
          <DebouncedSearch
            onSearch={handleSearch}
            fetchKeywords={fetchAsyncKeywords}
            placeholder="输入内容，动态获取关键词..."
            debounceWait={500}
            loading={loading}
          />
        </div>

        {/* 受控组件 */}
        <div style={{ marginBottom: '40px' }}>
          <h2 style={{ marginBottom: '16px', fontSize: '18px', color: '#262626' }}>
            受控组件 (当前值: {searchValue})
          </h2>
          <DebouncedSearch
            value={searchValue}
            onSearch={handleSearch}
            onChange={setSearchValue}
            placeholder="这是一个受控组件..."
            loading={loading}
          />
        </div>

        {/* 不同尺寸 */}
        <div style={{ marginBottom: '40px' }}>
          <h2 style={{ marginBottom: '16px', fontSize: '18px', color: '#262626' }}>
            不同尺寸
          </h2>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
            <DebouncedSearch
              size="small"
              onSearch={handleSearch}
              placeholder="小尺寸搜索框"
            />
            <DebouncedSearch
              size="middle"
              onSearch={handleSearch}
              placeholder="中等尺寸搜索框"
            />
            <DebouncedSearch
              size="large"
              onSearch={handleSearch}
              placeholder="大尺寸搜索框"
            />
          </div>
        </div>

        {/* 禁用状态 */}
        <div style={{ marginBottom: '40px' }}>
          <h2 style={{ marginBottom: '16px', fontSize: '18px', color: '#262626' }}>
            禁用状态
          </h2>
          <DebouncedSearch
            disabled
            placeholder="禁用状态的搜索框"
            defaultValue="无法编辑的内容"
          />
        </div>

        {/* 功能说明 */}
        <div style={{
          padding: '24px',
          backgroundColor: '#fafafa',
          borderRadius: '8px',
          border: '1px solid #e8e8e8',
          marginBottom: '32px'
        }}>
          <h3 style={{
            marginBottom: '16px',
            color: '#595959',
            fontSize: '16px'
          }}>
            组件特性：
          </h3>
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
            <ul style={{
              color: '#8c8c8c',
              lineHeight: '1.8',
              margin: 0,
              paddingLeft: '20px'
            }}>
              <li>🔍 智能关键词提示</li>
              <li>⚡ 防抖提示框显示</li>
              <li>🎯 关键词高亮显示</li>
              <li>🔄 支持异步数据获取</li>
              <li>📱 响应式设计</li>
            </ul>
            <ul style={{
              color: '#8c8c8c',
              lineHeight: '1.8',
              margin: 0,
              paddingLeft: '20px'
            }}>
              <li>🎛️ 受控/非受控模式</li>
              <li>🎨 多种尺寸支持</li>
              <li>⌨️ 键盘导航支持</li>
              <li>🚫 禁用状态支持</li>
              <li>⏱️ 可调节防抖延迟</li>
            </ul>
          </div>
        </div>

        {/* 搜索结果 */}
        {loading && (
          <div style={{
            textAlign: 'center',
            padding: '40px',
            color: '#1890ff',
            fontSize: '16px'
          }}>
            <Spin size="large" />
            <div style={{ marginTop: '16px' }}>搜索中...</div>
          </div>
        )}

        {searchResults.length > 0 && !loading && (
          <div>
            <h3 style={{
              marginBottom: '20px',
              color: '#262626',
              fontSize: '20px',
              borderBottom: '2px solid #1890ff',
              paddingBottom: '8px'
            }}>
              搜索结果 ({searchResults.length} 条)
            </h3>
            <div style={{ display: 'grid', gap: '12px' }}>
              {searchResults.map((result, index) => (
                <div
                  key={index}
                  style={{
                    padding: '16px 20px',
                    backgroundColor: 'white',
                    border: '1px solid #e8e8e8',
                    borderRadius: '8px',
                    cursor: 'pointer',
                    transition: 'all 0.3s ease',
                    boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
                  }}
                  onMouseEnter={(e) => {
                    const target = e.currentTarget as HTMLElement;
                    target.style.borderColor = '#1890ff';
                    target.style.boxShadow = '0 4px 12px rgba(24, 144, 255, 0.15)';
                    target.style.transform = 'translateY(-2px)';
                  }}
                  onMouseLeave={(e) => {
                    const target = e.currentTarget as HTMLElement;
                    target.style.borderColor = '#e8e8e8';
                    target.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';
                    target.style.transform = 'translateY(0)';
                  }}
                >
                  <div style={{
                    fontSize: '16px',
                    color: '#262626',
                    lineHeight: '1.5'
                  }}>
                    {result}
                  </div>
                  <div style={{
                    fontSize: '12px',
                    color: '#8c8c8c',
                    marginTop: '8px'
                  }}>
                    结果 #{index + 1} • 相关度: {Math.floor(Math.random() * 30) + 70}%
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
