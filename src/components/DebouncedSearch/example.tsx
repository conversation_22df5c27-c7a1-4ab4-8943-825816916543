import React, { useState } from 'react';
import DebouncedSearch from './index';

// 基础使用示例
export const BasicExample = () => {
  const handleSearch = (value: string) => {
    console.log('搜索:', value);
  };

  return (
    <DebouncedSearch
      onSearch={handleSearch}
      placeholder="请输入搜索关键词"
    />
  );
};

// 防抖输入监听示例
export const DebounceExample = () => {
  const [inputValue, setInputValue] = useState('');

  const handleSearch = (value: string) => {
    console.log('搜索:', value);
  };

  const handleChange = (value: string) => {
    console.log('输入变化 (防抖):', value);
    setInputValue(value);
  };

  return (
    <div>
      <p>当前输入值: {inputValue}</p>
      <DebouncedSearch
        onSearch={handleSearch}
        onChange={handleChange}
        debounceWait={500}
        placeholder="输入后停顿500ms才会显示提示框"
      />
    </div>
  );
};

// 异步关键词获取示例
export const AsyncExample = () => {
  const fetchKeywords = async (query: string): Promise<string[]> => {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 300));
    
    const mockKeywords = [
      `${query} 教程`,
      `${query} 实战`,
      `${query} 进阶`,
      `${query} 最佳实践`
    ];
    
    return mockKeywords.filter(keyword => 
      keyword.toLowerCase().includes(query.toLowerCase())
    );
  };

  const handleSearch = (value: string) => {
    console.log('搜索:', value);
  };

  return (
    <DebouncedSearch
      onSearch={handleSearch}
      fetchKeywords={fetchKeywords}
      placeholder="输入内容，动态获取关键词"
    />
  );
};

// 受控组件示例
export const ControlledExample = () => {
  const [value, setValue] = useState('');

  const handleSearch = (searchValue: string) => {
    console.log('搜索:', searchValue);
  };

  return (
    <div>
      <p>当前值: {value}</p>
      <DebouncedSearch
        value={value}
        onChange={setValue}
        onSearch={handleSearch}
        placeholder="受控组件"
      />
      <button onClick={() => setValue('')}>清空</button>
    </div>
  );
};

// 自定义关键词示例
export const CustomKeywordsExample = () => {
  const customKeywords = [
    'React开发',
    'Vue.js',
    'Angular',
    'JavaScript',
    'TypeScript',
    'Node.js',
    'Python',
    'Java'
  ];

  const handleSearch = (value: string) => {
    console.log('搜索:', value);
  };

  return (
    <DebouncedSearch
      keywords={customKeywords}
      onSearch={handleSearch}
      maxSuggestions={5}
      placeholder="自定义关键词列表"
    />
  );
};
