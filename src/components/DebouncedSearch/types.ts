// 防抖搜索组件的类型定义
export interface DebouncedSearchProps {
  /** 搜索回调函数 */
  onSearch?: (value: string) => void;
  /** 输入变化回调（防抖） */
  onChange?: (value: string) => void;
  /** 占位符文本 */
  placeholder?: string;
  /** 防抖延迟时间（毫秒） */
  debounceWait?: number;
  /** 是否显示加载状态 */
  loading?: boolean;
  /** 自定义关键词数据源 */
  keywords?: string[];
  /** 是否启用关键词提示 */
  enableSuggestions?: boolean;
  /** 最大显示提示数量 */
  maxSuggestions?: number;
  /** 组件尺寸 */
  size?: 'small' | 'middle' | 'large';
  /** 是否允许清空 */
  allowClear?: boolean;
  /** 自定义样式 */
  style?: React.CSSProperties;
  /** 自定义类名 */
  className?: string;
  /** 初始值 */
  defaultValue?: string;
  /** 受控值 */
  value?: string;
  /** 是否禁用 */
  disabled?: boolean;
  /** 获取异步关键词的函数 */
  fetchKeywords?: (query: string) => Promise<string[]>;
}
