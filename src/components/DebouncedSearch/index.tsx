import React, { useState, useMemo, useCallback, useRef, useEffect } from 'react';
import { AutoComplete, Input, Spin } from 'antd';
import { SearchOutlined, CloseCircleOutlined } from '@ant-design/icons';
import { debounce } from 'lodash';

export interface DebouncedSearchProps {
  /** 搜索回调函数 */
  onSearch?: (value: string) => void;
  /** 输入变化回调（防抖） */
  onChange?: (value: string) => void;
  /** 占位符文本 */
  placeholder?: string;
  /** 防抖延迟时间（毫秒） */
  debounceWait?: number;
  /** 是否显示加载状态 */
  loading?: boolean;
  /** 自定义关键词数据源 */
  keywords?: string[];
  /** 是否启用关键词提示 */
  enableSuggestions?: boolean;
  /** 最大显示提示数量 */
  maxSuggestions?: number;
  /** 组件尺寸 */
  size?: 'small' | 'middle' | 'large';
  /** 是否允许清空 */
  allowClear?: boolean;
  /** 自定义样式 */
  style?: React.CSSProperties;
  /** 自定义类名 */
  className?: string;
  /** 初始值 */
  defaultValue?: string;
  /** 受控值 */
  value?: string;
  /** 是否禁用 */
  disabled?: boolean;
  /** 获取异步关键词的函数 */
  fetchKeywords?: (query: string) => Promise<string[]>;
}

// 默认关键词数据库
const DEFAULT_KEYWORDS = [
  'React开发', 'React Hook', 'React Router', 'React Native',
  'JavaScript ES6', 'JavaScript Promise', 'JavaScript async/await',
  'Vue.js', 'Vue3 Composition API', 'Vue Router',
  'Angular', 'Angular Material',
  'Node.js', 'Node.js Express',
  'Python Django', 'Python Flask',
  'Java Spring', 'Java SpringBoot',
  'TypeScript', 'TypeScript 类型',
  'HTML5', 'CSS3', 'CSS Grid', 'CSS Flexbox',
  '响应式设计', '移动端开发',
  'Webpack', 'Vite', '前端工程化',
  '性能优化', 'SEO优化',
  '数据库设计', 'MySQL', 'MongoDB', 'Redis',
  'API设计', 'RESTful API', 'GraphQL',
  '微服务架构', '云计算', 'Docker', 'Kubernetes',
  '敏捷开发', '代码规范', '单元测试', '自动化测试',
  'UI/UX设计', '产品设计'
];

const DebouncedSearch: React.FC<DebouncedSearchProps> = ({
  onSearch,
  onChange,
  placeholder = "请输入搜索关键词",
  debounceWait = 3000,
  loading = false,
  keywords = DEFAULT_KEYWORDS,
  enableSuggestions = true,
  maxSuggestions = 8,
  size = 'large',
  allowClear = true,
  style,
  className,
  defaultValue = '',
  value,
  disabled = false,
  fetchKeywords
}) => {
  const [searchText, setSearchText] = useState(defaultValue);
  const [open, setOpen] = useState(false);
  const [suggestionLoading, setSuggestionLoading] = useState(false);
  const [dynamicKeywords, setDynamicKeywords] = useState<string[]>([]);
  const [debouncedSearchText, setDebouncedSearchText] = useState(defaultValue);
  const searchRef = useRef<any>(null);
  // 受控组件处理
  const currentValue = value !== undefined ? value : searchText;

  // 防抖处理输入变化
  const debouncedOnChange = useCallback(
    debounce((val: string) => {
      onChange?.(val);
    }, debounceWait),
    [onChange, debounceWait]
  );

  // 防抖处理搜索文本更新（用于显示提示框）
  const debouncedUpdateSearchText = useCallback(
    debounce((val: string) => {
      setDebouncedSearchText(val);
      // 只有在防抖后才显示提示框
      if (enableSuggestions && val.trim().length > 0) {
        setOpen(true);
      } else {
        setOpen(false);
      }
    }, debounceWait),
    [debounceWait, enableSuggestions]
  );

  // 防抖处理关键词获取
  const debouncedFetchKeywords = useCallback(
    debounce(async (query: string) => {
      if (!fetchKeywords || !query.trim()) {
        setSuggestionLoading(false);
        return;
      }

      try {
        setSuggestionLoading(true);
        const result = await fetchKeywords(query);
        setDynamicKeywords(result);
      } catch (error) {
        console.error('获取关键词失败:', error);
        setDynamicKeywords([]);
      } finally {
        setSuggestionLoading(false);
      }
    }, debounceWait),
    [fetchKeywords, debounceWait]
  );

  // 获取当前使用的关键词列表
  const currentKeywords = fetchKeywords ? dynamicKeywords : keywords;

  // 根据防抖后的搜索文本过滤关键词
  const filteredOptions = useMemo(() => {
    if (!enableSuggestions || !debouncedSearchText.trim()) return [];

    const filtered = currentKeywords
      .filter(keyword =>
        keyword.toLowerCase().includes(debouncedSearchText.toLowerCase())
      )
      .slice(0, maxSuggestions)
      .map(keyword => ({
        value: keyword,
        label: (
          <div style={{
            padding: '4px 0',
            display: 'flex',
            alignItems: 'center',
            fontSize: '14px'
          }}>
            <SearchOutlined style={{ marginRight: 8, color: '#1890ff' }} />
            <span dangerouslySetInnerHTML={{
              __html: keyword.replace(
                new RegExp(`(${debouncedSearchText})`, 'gi'),
                '<span style="color: #1890ff; font-weight: 500;">$1</span>'
              )
            }} />
          </div>
        )
      }));

    return filtered;
  }, [debouncedSearchText, currentKeywords, enableSuggestions, maxSuggestions]);

  // 处理输入变化
  const handleInputChange = useCallback((val: string) => {
    if (value === undefined) {
      setSearchText(val);
    }

    // 立即隐藏下拉框（如果输入为空）
    if (!val.trim()) {
      setOpen(false);
      setDebouncedSearchText('');
      setDynamicKeywords([]);
      setSuggestionLoading(false);
    } else {
      // 显示加载状态（如果启用了建议功能）
      if (enableSuggestions) {
        setSuggestionLoading(true);
      }
    }

    // 触发防抖回调
    debouncedOnChange(val);

    // 防抖更新搜索文本（这会控制提示框的显示）
    debouncedUpdateSearchText(val);

    // 如果有异步获取关键词的函数，触发获取
    if (fetchKeywords) {
      debouncedFetchKeywords(val);
    }
  }, [value, debouncedOnChange, debouncedUpdateSearchText, debouncedFetchKeywords, fetchKeywords, enableSuggestions]);

  // 处理选择关键词
  const handleSelect = useCallback((val: string) => {
    if (value === undefined) {
      setSearchText(val);
    }
    setOpen(false);
    onSearch?.(val);
  }, [value, onSearch]);

  // 处理回车搜索
  const handlePressEnter = useCallback((e: React.KeyboardEvent<HTMLInputElement>) => {
    const val = (e.target as HTMLInputElement).value;
    setOpen(false);
    onSearch?.(val);
  }, [onSearch]);

  // 处理搜索按钮点击
  const handleSearchClick = useCallback((val: string) => {
    setOpen(false);
    onSearch?.(val);
  }, [onSearch]);

  // 处理清空
  const handleClear = useCallback(() => {
    if (value === undefined) {
      setSearchText('');
    }
    setOpen(false);
    setDebouncedSearchText('');
    setDynamicKeywords([]);
    setSuggestionLoading(false);
    debouncedOnChange('');
    onSearch?.('');
  }, [value, debouncedOnChange, onSearch]);

  // 清理防抖函数
  useEffect(() => {
    return () => {
      debouncedOnChange.cancel();
      debouncedUpdateSearchText.cancel();
      debouncedFetchKeywords.cancel();
    };
  }, [debouncedOnChange, debouncedUpdateSearchText, debouncedFetchKeywords]);

  return (
    <div style={{ width: '100%', ...style }} className={className}>
      <AutoComplete
        style={{ width: '100%' }}
        options={filteredOptions}
        onSelect={handleSelect}
        onSearch={handleInputChange}
        value={currentValue}
        open={open && !disabled}
        onDropdownVisibleChange={setOpen}
        dropdownMatchSelectWidth={true}
        disabled={disabled}
        dropdownStyle={{
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
          borderRadius: '6px',
          border: '1px solid #d9d9d9'
        }}
        listHeight={300}
        notFoundContent={
          suggestionLoading ? (
            <div style={{
              padding: '12px 16px',
              textAlign: 'center'
            }}>
              <Spin size="small" />
              <span style={{ marginLeft: 8 }}>正在加载关键词...</span>
            </div>
          ) : debouncedSearchText.trim() && enableSuggestions && filteredOptions.length === 0 ? (
            <div style={{
              padding: '12px 16px',
              color: '#999',
              textAlign: 'center'
            }}>
              暂无匹配的关键词
            </div>
          ) : null
        }
      >
        <Input.Search
          ref={searchRef}
          placeholder={placeholder}
          enterButton={
            loading ? (
              <Spin size="small" />
            ) : (
              <SearchOutlined />
            )
          }
          size={size}
          allowClear={allowClear}
          disabled={disabled}
          onPressEnter={handlePressEnter}
          onSearch={handleSearchClick}
          onClear={handleClear}
          style={{
            borderRadius: '6px'
          }}
        />
      </AutoComplete>
    </div>
  );
};

// 导出主组件
export default DebouncedSearch;

// 使用示例组件
export const SearchDemo = () => {
  const [searchResults, setSearchResults] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchValue, setSearchValue] = useState('');
  const [asyncKeywords, setAsyncKeywords] = useState<string[]>([]);

  // 模拟异步获取关键词
  const fetchAsyncKeywords = async (query: string): Promise<string[]> => {
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 500));

    const mockApiKeywords = [
      `${query} 教程`,
      `${query} 实战`,
      `${query} 进阶`,
      `${query} 最佳实践`,
      `${query} 性能优化`,
      `${query} 源码解析`,
      `${query} 面试题`,
      `${query} 项目实战`
    ];

    return mockApiKeywords.filter(keyword =>
      keyword.toLowerCase().includes(query.toLowerCase())
    );
  };

  const handleSearch = (keyword: string) => {
    console.log('搜索关键词:', keyword);
    setLoading(true);

    // 模拟搜索API调用
    setTimeout(() => {
      const mockResults = [
        `关于 "${keyword}" 的搜索结果 1 - 这是一个详细的搜索结果描述`,
        `关于 "${keyword}" 的搜索结果 2 - 包含相关的技术文档和教程`,
        `关于 "${keyword}" 的搜索结果 3 - 实际项目案例和最佳实践`,
        `关于 "${keyword}" 的搜索结果 4 - 社区讨论和问题解答`,
        `关于 "${keyword}" 的搜索结果 5 - 最新的技术动态和更新`
      ];
      setSearchResults(mockResults);
      setLoading(false);
    }, 1000);
  };

  const handleInputChange = (value: string) => {
    console.log('输入变化 (防抖):', value);
    setSearchValue(value);
  };

  return (
    <div style={{ padding: '40px', backgroundColor: '#f5f5f5', minHeight: '100vh' }}>
      <div style={{
        maxWidth: '1000px',
        margin: '0 auto',
        backgroundColor: 'white',
        borderRadius: '12px',
        padding: '40px',
        boxShadow: '0 2px 16px rgba(0, 0, 0, 0.08)'
      }}>
        <h1 style={{
          textAlign: 'center',
          marginBottom: '40px',
          color: '#262626',
          fontSize: '28px',
          fontWeight: '600'
        }}>
          防抖搜索组件演示
        </h1>

        {/* 基础用法 */}
        <div style={{ marginBottom: '40px' }}>
          <h2 style={{ marginBottom: '16px', fontSize: '18px', color: '#262626' }}>
            基础用法 - 防抖延迟 300ms
          </h2>
          <p style={{ marginBottom: '16px', color: '#8c8c8c', fontSize: '14px' }}>
            💡 输入后停顿 300ms 才会显示提示框，避免频繁触发
          </p>
          <DebouncedSearch
            onSearch={handleSearch}
            onChange={handleInputChange}
            placeholder="输入关键词，停顿后显示提示..."
            loading={loading}
            debounceWait={300}
          />
        </div>

        {/* 快速响应演示 */}
        <div style={{ marginBottom: '40px' }}>
          <h2 style={{ marginBottom: '16px', fontSize: '18px', color: '#262626' }}>
            快速响应 - 防抖延迟 100ms
          </h2>
          <p style={{ marginBottom: '16px', color: '#8c8c8c', fontSize: '14px' }}>
            ⚡ 更短的防抖延迟，响应更快但可能触发更频繁
          </p>
          <DebouncedSearch
            onSearch={handleSearch}
            onChange={handleInputChange}
            placeholder="快速响应模式..."
            loading={loading}
            debounceWait={100}
          />
        </div>

        {/* 慢速响应演示 */}
        <div style={{ marginBottom: '40px' }}>
          <h2 style={{ marginBottom: '16px', fontSize: '18px', color: '#262626' }}>
            慢速响应 - 防抖延迟 800ms
          </h2>
          <p style={{ marginBottom: '16px', color: '#8c8c8c', fontSize: '14px' }}>
            🐌 更长的防抖延迟，减少触发频率，适合复杂搜索
          </p>
          <DebouncedSearch
            onSearch={handleSearch}
            onChange={handleInputChange}
            placeholder="慢速响应模式，停顿800ms后显示..."
            loading={loading}
            debounceWait={800}
          />
        </div>

        {/* 异步关键词获取 */}
        <div style={{ marginBottom: '40px' }}>
          <h2 style={{ marginBottom: '16px', fontSize: '18px', color: '#262626' }}>
            异步关键词获取 - 防抖延迟 500ms
          </h2>
          <p style={{ marginBottom: '16px', color: '#8c8c8c', fontSize: '14px' }}>
            🔄 从API动态获取关键词，防抖避免频繁请求
          </p>
          <DebouncedSearch
            onSearch={handleSearch}
            fetchKeywords={fetchAsyncKeywords}
            placeholder="输入内容，动态获取关键词..."
            debounceWait={500}
            loading={loading}
          />
        </div>

        {/* 受控组件 */}
        <div style={{ marginBottom: '40px' }}>
          <h2 style={{ marginBottom: '16px', fontSize: '18px', color: '#262626' }}>
            受控组件 (当前值: {searchValue})
          </h2>
          <DebouncedSearch
            value={searchValue}
            onSearch={handleSearch}
            onChange={setSearchValue}
            placeholder="这是一个受控组件..."
            loading={loading}
          />
        </div>

        {/* 不同尺寸 */}
        <div style={{ marginBottom: '40px' }}>
          <h2 style={{ marginBottom: '16px', fontSize: '18px', color: '#262626' }}>
            不同尺寸
          </h2>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
            <DebouncedSearch
              size="small"
              onSearch={handleSearch}
              placeholder="小尺寸搜索框"
            />
            <DebouncedSearch
              size="middle"
              onSearch={handleSearch}
              placeholder="中等尺寸搜索框"
            />
            <DebouncedSearch
              size="large"
              onSearch={handleSearch}
              placeholder="大尺寸搜索框"
            />
          </div>
        </div>

        {/* 禁用状态 */}
        <div style={{ marginBottom: '40px' }}>
          <h2 style={{ marginBottom: '16px', fontSize: '18px', color: '#262626' }}>
            禁用状态
          </h2>
          <DebouncedSearch
            disabled
            placeholder="禁用状态的搜索框"
            defaultValue="无法编辑的内容"
          />
        </div>

        {/* 功能说明 */}
        <div style={{
          padding: '24px',
          backgroundColor: '#fafafa',
          borderRadius: '8px',
          border: '1px solid #e8e8e8',
          marginBottom: '32px'
        }}>
          <h3 style={{
            marginBottom: '16px',
            color: '#595959',
            fontSize: '16px'
          }}>
            组件特性：
          </h3>
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
            <ul style={{
              color: '#8c8c8c',
              lineHeight: '1.8',
              margin: 0,
              paddingLeft: '20px'
            }}>
              <li>🔍 智能关键词提示</li>
              <li>⚡ 防抖提示框显示</li>
              <li>🎯 关键词高亮显示</li>
              <li>🔄 支持异步数据获取</li>
              <li>📱 响应式设计</li>
            </ul>
            <ul style={{
              color: '#8c8c8c',
              lineHeight: '1.8',
              margin: 0,
              paddingLeft: '20px'
            }}>
              <li>🎛️ 受控/非受控模式</li>
              <li>🎨 多种尺寸支持</li>
              <li>⌨️ 键盘导航支持</li>
              <li>🚫 禁用状态支持</li>
              <li>⏱️ 可调节防抖延迟</li>
            </ul>
          </div>
        </div>

        {/* 搜索结果 */}
        {loading && (
          <div style={{
            textAlign: 'center',
            padding: '40px',
            color: '#1890ff',
            fontSize: '16px'
          }}>
            <Spin size="large" />
            <div style={{ marginTop: '16px' }}>搜索中...</div>
          </div>
        )}

        {searchResults.length > 0 && !loading && (
          <div>
            <h3 style={{
              marginBottom: '20px',
              color: '#262626',
              fontSize: '20px',
              borderBottom: '2px solid #1890ff',
              paddingBottom: '8px'
            }}>
              搜索结果 ({searchResults.length} 条)
            </h3>
            <div style={{ display: 'grid', gap: '12px' }}>
              {searchResults.map((result, index) => (
                <div
                  key={index}
                  style={{
                    padding: '16px 20px',
                    backgroundColor: 'white',
                    border: '1px solid #e8e8e8',
                    borderRadius: '8px',
                    cursor: 'pointer',
                    transition: 'all 0.3s ease',
                    boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
                  }}
                  onMouseEnter={(e) => {
                    const target = e.currentTarget as HTMLElement;
                    target.style.borderColor = '#1890ff';
                    target.style.boxShadow = '0 4px 12px rgba(24, 144, 255, 0.15)';
                    target.style.transform = 'translateY(-2px)';
                  }}
                  onMouseLeave={(e) => {
                    const target = e.currentTarget as HTMLElement;
                    target.style.borderColor = '#e8e8e8';
                    target.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';
                    target.style.transform = 'translateY(0)';
                  }}
                >
                  <div style={{
                    fontSize: '16px',
                    color: '#262626',
                    lineHeight: '1.5'
                  }}>
                    {result}
                  </div>
                  <div style={{
                    fontSize: '12px',
                    color: '#8c8c8c',
                    marginTop: '8px'
                  }}>
                    结果 #{index + 1} • 相关度: {Math.floor(Math.random() * 30) + 70}%
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
