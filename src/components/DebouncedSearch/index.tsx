import Search from "antd/es/input/Search";
import {SearchProps} from "antd/lib/input";

interface DebouncedSearchProps extends SearchProps{
  wait?: number
  onSearch: (value: string) => void

}
const DebouncedSearch : React.FC<DebouncedSearchProps> = (props)=>{
  const
  return (
    <Search placeholder="input search text" onSearch={props.onSearch} enterButton />
  );
};
export default DebouncedSearch;
