import React, { useState, useMemo, useCallback, useRef, useEffect } from 'react';
import { AutoComplete, Input, Spin } from 'antd';
import { SearchOutlined} from '@ant-design/icons';
import { debounce } from 'lodash';

export interface DebouncedSearchProps {
  /** 搜索回调函数 */
  onSearch?: (value: string) => void;
  /** 输入变化回调（防抖） */
  onChange?: (value: string) => void;
  /** 占位符文本 */
  placeholder?: string;
  /** 防抖延迟时间（毫秒） */
  debounceWait?: number;
  /** 是否显示加载状态 */
  loading?: boolean;
  /** 自定义关键词数据源 */
  keywords?: string[];
  /** 是否启用关键词提示 */
  enableSuggestions?: boolean;
  /** 最大显示提示数量 */
  maxSuggestions?: number;
  /** 组件尺寸 */
  size?: 'small' | 'middle' | 'large';
  /** 是否允许清空 */
  allowClear?: boolean;
  /** 自定义样式 */
  style?: React.CSSProperties;
  /** 自定义类名 */
  className?: string;
  /** 初始值 */
  defaultValue?: string;
  /** 受控值 */
  value?: string;
  /** 是否禁用 */
  disabled?: boolean;
  /** 获取异步关键词的函数 */
  fetchKeywords?: (query: string) => Promise<string[]>;
}

// 默认关键词数据库
const DEFAULT_KEYWORDS = [
  'React开发', 'React Hook', 'React Router', 'React Native',
  'JavaScript ES6', 'JavaScript Promise', 'JavaScript async/await',
  'Vue.js', 'Vue3 Composition API', 'Vue Router',
  'Angular', 'Angular Material',
  'Node.js', 'Node.js Express',
  'Python Django', 'Python Flask',
  'Java Spring', 'Java SpringBoot',
  'TypeScript', 'TypeScript 类型',
  'HTML5', 'CSS3', 'CSS Grid', 'CSS Flexbox',
  '响应式设计', '移动端开发',
  'Webpack', 'Vite', '前端工程化',
  '性能优化', 'SEO优化',
  '数据库设计', 'MySQL', 'MongoDB', 'Redis',
  'API设计', 'RESTful API', 'GraphQL',
  '微服务架构', '云计算', 'Docker', 'Kubernetes',
  '敏捷开发', '代码规范', '单元测试', '自动化测试',
  'UI/UX设计', '产品设计'
];

const DebouncedSearch: React.FC<DebouncedSearchProps> = ({
  onSearch,
  onChange,
  placeholder = "请输入搜索关键词",
  debounceWait = 3000,
  loading = false,
  keywords = DEFAULT_KEYWORDS,
  enableSuggestions = true,
  maxSuggestions = 8,
  size = 'large',
  allowClear = true,
  style,
  className,
  defaultValue = '',
  value,
  disabled = false,
  fetchKeywords
}) => {
  const [searchText, setSearchText] = useState(defaultValue);
  const [open, setOpen] = useState(false);
  const [suggestionLoading, setSuggestionLoading] = useState(false);
  const [dynamicKeywords, setDynamicKeywords] = useState<string[]>([]);
  const [debouncedSearchText, setDebouncedSearchText] = useState(defaultValue);
  const searchRef = useRef<any>(null);
  // 受控组件处理
  const currentValue = value !== undefined ? value : searchText;

  // 防抖处理输入变化
  const debouncedOnChange = useCallback(
    debounce((val: string) => {
      onChange?.(val);
    }, debounceWait),
    [onChange, debounceWait]
  );

  // 防抖处理搜索文本更新（用于显示提示框）
  const debouncedUpdateSearchText = useCallback(
    debounce((val: string) => {
      setDebouncedSearchText(val);
      // 只有在防抖后才显示提示框
      if (enableSuggestions && val.trim().length > 0) {
        setOpen(true);
      } else {
        setOpen(false);
      }
    }, debounceWait),
    [debounceWait, enableSuggestions]
  );

  // 防抖处理关键词获取
  const debouncedFetchKeywords = useCallback(
    debounce(async (query: string) => {
      if (!fetchKeywords || !query.trim()) {
        setSuggestionLoading(false);
        return;
      }

      try {
        setSuggestionLoading(true);
        const result = await fetchKeywords(query);
        setDynamicKeywords(result);
      } catch (error) {
        console.error('获取关键词失败:', error);
        setDynamicKeywords([]);
      } finally {
        setSuggestionLoading(false);
      }
    }, debounceWait),
    [fetchKeywords, debounceWait]
  );

  // 获取当前使用的关键词列表
  const currentKeywords = fetchKeywords ? dynamicKeywords : keywords;

  // 根据防抖后的搜索文本过滤关键词
  const filteredOptions = useMemo(() => {
    if (!enableSuggestions || !debouncedSearchText.trim()) return [];

    const filtered = currentKeywords
      .filter(keyword =>
        keyword.toLowerCase().includes(debouncedSearchText.toLowerCase())
      )
      .slice(0, maxSuggestions)
      .map(keyword => ({
        value: keyword,
        label: (
          <div style={{
            padding: '4px 0',
            display: 'flex',
            alignItems: 'center',
            fontSize: '14px'
          }}>
            <SearchOutlined style={{ marginRight: 8, color: '#1890ff' }} />
            <span dangerouslySetInnerHTML={{
              __html: keyword.replace(
                new RegExp(`(${debouncedSearchText})`, 'gi'),
                '<span style="color: #1890ff; font-weight: 500;">$1</span>'
              )
            }} />
          </div>
        )
      }));

    return filtered;
  }, [debouncedSearchText, currentKeywords, enableSuggestions, maxSuggestions]);

  // 处理输入变化
  const handleInputChange = useCallback((val: string) => {
    if (value === undefined) {
      setSearchText(val);
    }

    // 立即隐藏下拉框（如果输入为空）
    if (!val.trim()) {
      setOpen(false);
      setDebouncedSearchText('');
      setDynamicKeywords([]);
      setSuggestionLoading(false);
    } else {
      // 显示加载状态（如果启用了建议功能）
      if (enableSuggestions) {
        setSuggestionLoading(true);
      }
    }

    // 触发防抖回调
    debouncedOnChange(val);

    // 防抖更新搜索文本（这会控制提示框的显示）
    debouncedUpdateSearchText(val);

    // 如果有异步获取关键词的函数，触发获取
    if (fetchKeywords) {
      debouncedFetchKeywords(val);
    }
  }, [value, debouncedOnChange, debouncedUpdateSearchText, debouncedFetchKeywords, fetchKeywords, enableSuggestions]);

  // 处理选择关键词
  const handleSelect = useCallback((val: string) => {
    if (value === undefined) {
      setSearchText(val);
    }
    setOpen(false);
    onSearch?.(val);
  }, [value, onSearch]);

  // 处理回车搜索
  const handlePressEnter = useCallback((e: React.KeyboardEvent<HTMLInputElement>) => {
    const val = (e.target as HTMLInputElement).value;
    setOpen(false);
    onSearch?.(val);
  }, [onSearch]);

  // 处理搜索按钮点击
  const handleSearchClick = useCallback((val: string) => {
    setOpen(false);
    onSearch?.(val);
  }, [onSearch]);

  // 处理清空
  const handleClear = useCallback(() => {
    if (value === undefined) {
      setSearchText('');
    }
    setOpen(false);
    setDebouncedSearchText('');
    setDynamicKeywords([]);
    setSuggestionLoading(false);
    debouncedOnChange('');
    onSearch?.('');
  }, [value, debouncedOnChange, onSearch]);

  // 清理防抖函数
  useEffect(() => {
    return () => {
      debouncedOnChange.cancel();
      debouncedUpdateSearchText.cancel();
      debouncedFetchKeywords.cancel();
    };
  }, [debouncedOnChange, debouncedUpdateSearchText, debouncedFetchKeywords]);

  return (
    <div style={{ width: '100%', ...style }} className={className}>
      <AutoComplete
        style={{ width: '100%' }}
        options={filteredOptions}
        onSelect={handleSelect}
        onSearch={handleInputChange}
        value={currentValue}
        open={open && !disabled}
        onDropdownVisibleChange={setOpen}
        disabled={disabled}
        dropdownStyle={{
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
          borderRadius: '6px',
          border: '1px solid #d9d9d9'
        }}
        listHeight={300}
        notFoundContent={
          suggestionLoading ? (
            <div style={{
              padding: '12px 16px',
              textAlign: 'center'
            }}>
              <Spin size="small" />
              <span style={{ marginLeft: 8 }}>正在加载关键词...</span>
            </div>
          ) : debouncedSearchText.trim() && enableSuggestions && filteredOptions.length === 0 ? (
            <div style={{
              padding: '12px 16px',
              color: '#999',
              textAlign: 'center'
            }}>
              暂无匹配的关键词
            </div>
          ) : null
        }
      >
        <Input.Search
          ref={searchRef}
          placeholder={placeholder}
          enterButton={
            loading ? (
              <Spin size="small" />
            ) : (
              <SearchOutlined />
            )
          }
          size={size}
          allowClear={allowClear}
          disabled={disabled}
          onPressEnter={handlePressEnter}
          onSearch={handleSearchClick}
          onClear={handleClear}
          style={{
            borderRadius: '6px'
          }}
        />
      </AutoComplete>
    </div>
  );
};

// 导出主组件
export default DebouncedSearch;


