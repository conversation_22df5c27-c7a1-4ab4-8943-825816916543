import { AutoComplete, Input, Spin } from "antd";
import { SearchProps } from "antd/es/input";
import React, { useState, useRef, useEffect, useCallback } from "react";
import { SearchOutlined } from "@ant-design/icons";

interface SuggestionOption {
  value: string;
  label: string;
}

interface NewDebouncedSearchProps extends SearchProps {
  wait?: number; // 防抖延迟时间，默认300ms
  enableSuggestions?: boolean; // 是否启用关键词建议
  suggestions?: SuggestionOption[]; // 建议关键词数据
  onSearch?: (value: string) => void; // 搜索回调
  onSuggestionSearch?: (value: string) => void; // 获取建议关键词的回调
  maxSuggestions?: number; // 最大建议数量
}

const NewDebouncedSearch: React.FC<NewDebouncedSearchProps> = (props) => {
  const {
    wait = 300,
    enableSuggestions = true,
    suggestions = [],
    onSuggestionSearch,
    maxSuggestions = 10,
    ...restProps
  } = props;

  const [currentValue, setCurrentValue] = useState<string>('');
  const [debouncedSearchText, setDebouncedSearchText] = useState<string>('');
  const [open, setOpen] = useState<boolean>(false);
  const [suggestionLoading, setSuggestionLoading] = useState<boolean>(false);
  const [filteredOptions, setFilteredOptions] = useState<SuggestionOption[]>([]);

  const searchRef = useRef<any>(null);
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);

  // 防抖处理
  const debouncedSetSearchText = useCallback((text: string) => {
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }

    debounceTimerRef.current = setTimeout(() => {
      setDebouncedSearchText(text);
    }, wait);
  }, [wait]);

  // 处理输入变化
  const handleInputChange = useCallback((value: string) => {
    setCurrentValue(value);
    setOpen(!!value.trim()); // 有输入内容时显示下拉框

    if (enableSuggestions) {
      debouncedSetSearchText(value);
    }
  }, [enableSuggestions, debouncedSetSearchText]);

  // 处理选择建议项
  const handleSelect = useCallback((value: string) => {
    setCurrentValue(value);
    setOpen(false);
    if (props.onSearch) {
      props.onSearch(value);
    }
  }, [props]);

  // 监听防抖后的搜索文本变化，获取建议
  useEffect(() => {
    if (!enableSuggestions || !debouncedSearchText.trim()) {
      setFilteredOptions([]);
      setSuggestionLoading(false);
      return;
    }

    // 如果有本地建议数据，直接过滤
    if (suggestions.length > 0) {
      const filtered = suggestions
        .filter(option =>
          option.label.toLowerCase().includes(debouncedSearchText.toLowerCase()) ||
          option.value.toLowerCase().includes(debouncedSearchText.toLowerCase())
        )
        .slice(0, maxSuggestions);
      setFilteredOptions(filtered);
      return;
    }

    // 如果有异步获取建议的回调
    if (onSuggestionSearch) {
      setSuggestionLoading(true);
      onSuggestionSearch(debouncedSearchText);
    }
  }, [debouncedSearchText, enableSuggestions, suggestions, maxSuggestions, onSuggestionSearch]);

  // 监听外部传入的建议数据变化
  useEffect(() => {
    if (suggestions.length > 0 && debouncedSearchText.trim()) {
      const filtered = suggestions
        .filter(option =>
          option.label.toLowerCase().includes(debouncedSearchText.toLowerCase()) ||
          option.value.toLowerCase().includes(debouncedSearchText.toLowerCase())
        )
        .slice(0, maxSuggestions);
      setFilteredOptions(filtered);
      setSuggestionLoading(false);
    } else if (suggestions.length > 0) {
      setSuggestionLoading(false);
    }
  }, [suggestions, debouncedSearchText, maxSuggestions]);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, []);

  return (
    <AutoComplete
      style={{ width: '100%' }}
      options={filteredOptions}
      onSelect={handleSelect}
      onSearch={handleInputChange}
      value={currentValue}
      open={open && !restProps.disabled}
      onDropdownVisibleChange={setOpen}
      disabled={restProps.disabled}
      listHeight={300}
      notFoundContent={
        suggestionLoading ? (
          <div style={{
            padding: '12px 16px',
            textAlign: 'center'
          }}>
            <Spin size="small" />
            <span style={{ marginLeft: 8 }}>正在加载关键词...</span>
          </div>
        ) : debouncedSearchText.trim() && enableSuggestions && filteredOptions.length === 0 ? (
          <div style={{
            padding: '12px 16px',
            color: '#999',
            textAlign: 'center'
          }}>
            暂无匹配的关键词
          </div>
        ) : null
      }
    >
      <Input.Search
        ref={searchRef}
        placeholder={restProps.placeholder}
        enterButton={
          restProps.loading ? (
            <Spin size="small" />
          ) : (
            <SearchOutlined />
          )
        }
        size={restProps.size}
        allowClear={restProps.allowClear}
        disabled={restProps.disabled}
        onPressEnter={restProps.onPressEnter}
        onSearch={restProps.onSearch}
        style={{
          borderRadius: '6px'
        }}
      />
    </AutoComplete>
  );
};

export default NewDebouncedSearch;
