import {APP_ENV} from "@/constants";
import { mockConfig } from "./mock-config";

export const requestConfig = {
  // 根据mock配置决定是否使用真实API
  baseURL: mockConfig.enabled ? '' : (APP_ENV === 'development' ? 'http://asset-api.zhuanzhitech.com' : 'http://asset-api.zhuanzhitech.com'),
  timeout: 5000,
  retryTimes: 1,
  baseHeaders: {
    'Accept': 'application/json',
    'Content-Type': 'application/json',
  },
  isRefresh: !mockConfig.enabled,  // Mock模式下禁用token刷新
  useMock: mockConfig.enabled,     // 使用mock配置
} as const;
