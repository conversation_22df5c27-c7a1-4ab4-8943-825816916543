// Mock配置文件
import { APP_ENV } from "@/constants";

export const mockConfig = {
  // 是否启用Mock数据（开发环境默认启用）
  enabled: APP_ENV === 'development',
  
  // Mock延迟时间（毫秒）
  delay: 300,
  
  // 是否在控制台显示Mock日志
  showLogs: true,
  
  // Mock数据版本
  version: '1.0.0'
} as const;

// 运行时控制Mock状态的函数
export const setMockEnabled = (enabled: boolean) => {
  (mockConfig as any).enabled = enabled;
  console.log(`Mock数据${enabled ? '已启用' : '已禁用'}`);
};

// 获取Mock状态
export const isMockEnabled = () => mockConfig.enabled;
