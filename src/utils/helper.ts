import { useIntl as intlHandle } from '@umijs/max';
import { produce } from 'immer';
import { get, has, isEmpty, set } from 'lodash';

/**
 * 过期时间计算
 * @param expTime
 */
export const localDataExpire = (expTime: number): boolean => {
  const currentTime = Date.now();
  const currentS = Math.floor(currentTime / 1000);
  const timeS = Math.floor(expTime / 1000);

  const diffTime = timeS - currentS;
  return diffTime <= 0;
};

/**
 * 千分位格式化
 * @param value
 */
export const millennials = (value: string) => {
  const num = parseFloat(value);
  if (isNaN(num)) {
    return '';
  }

  return num.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
};

/**
 * 国际化使用
 * @param id
 * @param defaultValue
 */
export const t = (id, defaultValue = '') => {
  const intl = intlHandle();
  return intl.formatMessage({ id, defaultMessage: defaultValue });
};

/**
 * 统一状态处理
 * @param currentState
 * @param path
 * @param value
 * @param limit
 */
export const handleParseStateChange: any = (
  currentState: any,
  path: string,
  value: any,
  limit = ':',
) => {
  const parts = path.split(limit);
  return produce(currentState, (draft) => {
    parts.reduce((acc, current, index) => {
      // 如果是最后一个元素，设置值
      if (index === parts.length - 1) {
        acc[current] = value;
        return null; // 最后一步不需要返回值
      }

      acc[current] = acc[current] || {};
      return acc[current];
    }, draft);
  });
};

// 现场10秒，网页15秒
export const handlePauseState = (
  isPause: boolean,
  pathRefs: { [key: string]: any },
  path: string,
  timeout: number = 15000,
) => {
  // 鼠标点入后的暂停处理
  if (isPause) {
    // 如果没有这个path，则初始化这个path，进入暂停
    if (!has(pathRefs.current, path)) {
      pathRefs.current[path] = null;
    } else {
      // 如果有这个path了，并且这个path有值（定时器id），则清理掉上次的定时器，但是保持暂停
      if (pathRefs.current[path]) {
        clearTimeout(pathRefs.current[path] as unknown as number);
      }

      console.log('112233');
    }

    return;
  }

  // isPause = false，鼠标离开了
  // 如果已经有定时器了，则先停止，再启动
  if (pathRefs.current[path]) {
    clearTimeout(pathRefs.current[path] as unknown as number);
  }

  console.log('445566');
  // 定时器时间到了之后，删掉key
  pathRefs.current[path] = setTimeout(() => {
    console.log(`${path} timer over`);
    delete pathRefs.current[path];
  }, timeout);
};

// teble1:name:1 => 10
export const handleMergeStatesByPath = (path, preState, newState, limit = ':') => {
  if ('' === path || isEmpty(path)) {
    return newState;
  }

  const pathArr = path.split(limit);
  set(newState, pathArr, get(preState, pathArr));
  return newState;
};

export const getTableScroll = (window) => {
  // 计算除了表格之外的其他区域高度
  // 页头 + padding + 表格头部 + 分页器 + 底部间距
  const otherHeight = 64 + 24 + 40 + 24 + 24;
  // 返回一个略小于视窗高度的值，确保滚动条在可视区域内
  return (window.innerHeight - otherHeight) / 1.35;
};
