import {localStorageService} from "@/utils/local-storage-service";
import {history} from "@umijs/max";
import {appConfig} from "@/config/app-config";

const loginPath = appConfig.loginPath;
const homePath = appConfig.homePath;

export const logoutHandle = (saveRedirect = false) => {
  let path;
  localStorageService.clear();
  const {location} = history;
  if (saveRedirect) {
    history.replace(loginPath);
    return;
  }

  if (location.pathname !== loginPath) {
    path = location.pathname
    history.replace(`${loginPath}?redirect=${path}`);
  }

  return;
}

export const loginHandle = (data) => {
  const urlParams = new URL(window.location.href).searchParams;
  const path = urlParams.get('redirect') || homePath;
  localStorageService.setItem(appConfig.loginStorageKey, data);
  history.replace(path);
  return;
}

export const loginAndToHome = () => {

}
