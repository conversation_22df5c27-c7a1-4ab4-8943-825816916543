// Mock管理器 - 统一管理所有mock服务
import { userMockService } from './services/userService';
import { ruleMockService } from './services/ruleService';
import { noticeMockService } from './services/noticeService';
import { commonMockService } from './services/commonService';

export class MockManager {
  private static instance: MockManager;
  private isEnabled: boolean = true;

  static getInstance(): MockManager {
    if (!MockManager.instance) {
      MockManager.instance = new MockManager();
    }
    return MockManager.instance;
  }

  // 启用/禁用Mock
  setEnabled(enabled: boolean) {
    this.isEnabled = enabled;
    console.log(`Mock数据${enabled ? '已启用' : '已禁用'}`);
  }

  isEnabledMock(): boolean {
    return this.isEnabled;
  }

  // 获取用户服务
  getUserService() {
    return userMockService;
  }

  // 获取规则服务
  getRuleService() {
    return ruleMockService;
  }

  // 获取通知服务
  getNoticeService() {
    return noticeMockService;
  }

  // 获取通用服务
  getCommonService() {
    return commonMockService;
  }
}

export const mockManager = MockManager.getInstance();

// 导出所有服务
export {
  userMockService,
  ruleMockService,
  noticeMockService,
  commonMockService
};
