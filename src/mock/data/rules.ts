// 规则相关的模拟数据
export interface MockRule {
  key: number;
  disabled?: boolean;
  href: string;
  avatar: string;
  name: string;
  owner: string;
  desc: string;
  callNo: number;
  status: string;
  updatedAt: string;
  createdAt: string;
  progress: number;
}

export const mockRules: MockRule[] = [
  {
    key: 1,
    disabled: false,
    href: 'https://ant.design',
    avatar: 'https://gw.alipayobjects.com/zos/rmsportal/eeHMaZBwmTvLdIwMfBpg.png',
    name: '用户管理规则',
    owner: '张三',
    desc: '这是一段描述，用于管理用户权限和访问控制',
    callNo: 100000,
    status: '已上线',
    updatedAt: '2024-01-15',
    createdAt: '2024-01-01',
    progress: 100,
  },
  {
    key: 2,
    disabled: false,
    href: 'https://ant.design',
    avatar: 'https://gw.alipayobjects.com/zos/rmsportal/udxAbMEhpwthVVcjLXik.png',
    name: '数据安全规则',
    owner: '李四',
    desc: '这是一段描述，用于保护系统数据安全',
    callNo: 80000,
    status: '已上线',
    updatedAt: '2024-01-14',
    createdAt: '2024-01-02',
    progress: 95,
  },
  {
    key: 3,
    disabled: false,
    href: 'https://ant.design',
    avatar: 'https://gw.alipayobjects.com/zos/rmsportal/eeHMaZBwmTvLdIwMfBpg.png',
    name: '接口访问规则',
    owner: '王五',
    desc: '这是一段描述，用于控制API接口的访问频率',
    callNo: 60000,
    status: '已上线',
    updatedAt: '2024-01-13',
    createdAt: '2024-01-03',
    progress: 88,
  },
  {
    key: 4,
    disabled: true,
    href: 'https://ant.design',
    avatar: 'https://gw.alipayobjects.com/zos/rmsportal/kISTdvpyTAhtGxpovNWd.png',
    name: '文件上传规则',
    owner: '赵六',
    desc: '这是一段描述，用于限制文件上传的类型和大小',
    callNo: 40000,
    status: '已下线',
    updatedAt: '2024-01-12',
    createdAt: '2024-01-04',
    progress: 70,
  },
  {
    key: 5,
    disabled: false,
    href: 'https://ant.design',
    avatar: 'https://gw.alipayobjects.com/zos/rmsportal/udxAbMEhpwthVVcjLXik.png',
    name: '登录验证规则',
    owner: '孙七',
    desc: '这是一段描述，用于验证用户登录的安全性',
    callNo: 120000,
    status: '已上线',
    updatedAt: '2024-01-11',
    createdAt: '2024-01-05',
    progress: 100,
  },
  {
    key: 6,
    disabled: false,
    href: 'https://ant.design',
    avatar: 'https://gw.alipayobjects.com/zos/rmsportal/eeHMaZBwmTvLdIwMfBpg.png',
    name: '缓存清理规则',
    owner: '周八',
    desc: '这是一段描述，用于定期清理系统缓存',
    callNo: 30000,
    status: '已上线',
    updatedAt: '2024-01-10',
    createdAt: '2024-01-06',
    progress: 85,
  },
  {
    key: 7,
    disabled: false,
    href: 'https://ant.design',
    avatar: 'https://gw.alipayobjects.com/zos/rmsportal/kISTdvpyTAhtGxpovNWd.png',
    name: '日志记录规则',
    owner: '吴九',
    desc: '这是一段描述，用于记录系统操作日志',
    callNo: 90000,
    status: '已上线',
    updatedAt: '2024-01-09',
    createdAt: '2024-01-07',
    progress: 92,
  },
  {
    key: 8,
    disabled: false,
    href: 'https://ant.design',
    avatar: 'https://gw.alipayobjects.com/zos/rmsportal/udxAbMEhpwthVVcjLXik.png',
    name: '备份恢复规则',
    owner: '郑十',
    desc: '这是一段描述，用于系统数据的备份和恢复',
    callNo: 50000,
    status: '已上线',
    updatedAt: '2024-01-08',
    createdAt: '2024-01-08',
    progress: 78,
  }
];
