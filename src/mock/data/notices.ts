// 通知相关的模拟数据
export interface MockNotice {
  id: string;
  avatar?: string;
  title: string;
  datetime: string;
  type: string;
  read?: boolean;
  description?: string;
  clickClose?: boolean;
  extra?: string;
  status?: string;
}

export const mockNotices: MockNotice[] = [
  {
    id: '000000001',
    avatar: 'https://gw.alipayobjects.com/zos/rmsportal/ThXAXghbEsBCCSDihZxY.png',
    title: '你收到了 14 份新周报',
    datetime: '2024-01-15',
    type: 'notification',
    read: false,
    description: '请及时查看并处理相关事务',
    clickClose: true,
  },
  {
    id: '000000002',
    avatar: 'https://gw.alipayobjects.com/zos/rmsportal/OKJXDXrmkNshAMvwtvhu.png',
    title: '你推荐的 曲妮丝 已通过第三轮面试',
    datetime: '2024-01-14',
    type: 'notification',
    read: false,
    description: '恭喜你，推荐的候选人表现优秀',
    clickClose: true,
  },
  {
    id: '000000003',
    avatar: 'https://gw.alipayobjects.com/zos/rmsportal/kISTdvpyTAhtGxpovNWd.png',
    title: '这种模板可以区分多种通知类型',
    datetime: '2024-01-13',
    type: 'notification',
    read: false,
    description: '系统通知模板已更新',
    clickClose: true,
  },
  {
    id: '000000004',
    avatar: 'https://gw.alipayobjects.com/zos/rmsportal/GvqBnKhFgObvnSGkDsje.png',
    title: '左侧图标用于区分不同的类型',
    datetime: '2024-01-12',
    type: 'notification',
    read: true,
    description: '界面优化完成',
    clickClose: true,
  },
  {
    id: '000000005',
    avatar: 'https://gw.alipayobjects.com/zos/rmsportal/ThXAXghbEsBCCSDihZxY.png',
    title: '内容不要超过两行字，超出时自动截断',
    datetime: '2024-01-11',
    type: 'notification',
    read: false,
    description: '显示规范已更新',
    clickClose: true,
  },
  {
    id: '000000006',
    title: '曲丽丽 评论了你',
    description: '描述信息描述信息描述信息',
    datetime: '2024-01-10',
    type: 'message',
    read: false,
    clickClose: true,
  },
  {
    id: '000000007',
    title: '朱偏右 回复了你',
    description: '这种模板用于提醒谁与你发生了互动，左侧放『谁』的头像',
    datetime: '2024-01-09',
    type: 'message',
    read: false,
    clickClose: true,
  },
  {
    id: '000000008',
    title: '标题',
    description: '这种模板用于提醒谁与你发生了互动，左侧放『谁』的头像',
    datetime: '2024-01-08',
    type: 'message',
    read: true,
    clickClose: true,
  },
  {
    id: '000000009',
    title: '任务名称',
    description: '任务需要在 2024-01-20 前启动',
    datetime: '2024-01-07',
    type: 'event',
    read: false,
    extra: '未开始',
    status: 'todo',
  },
  {
    id: '000000010',
    title: '第三方紧急代码变更',
    description: '冠霖提交于 2024-01-06，需在 2024-01-07 前完成代码变更任务',
    datetime: '2024-01-06',
    type: 'event',
    read: false,
    extra: '马上到期',
    status: 'urgent',
  },
  {
    id: '000000011',
    title: '信息安全考试',
    description: '指派竹尔于 2024-01-09 前完成更新并发布',
    datetime: '2024-01-05',
    type: 'event',
    read: true,
    extra: '已耗时 8 天',
    status: 'doing',
  },
  {
    id: '000000012',
    title: 'ABCD 版本发布',
    description: '指派竹尔于 2024-01-04 前完成更新并发布',
    datetime: '2024-01-04',
    type: 'event',
    read: true,
    extra: '进行中',
    status: 'processing',
  },
];
