// 用户相关的模拟数据
export interface MockUser {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  phone?: string;
  status: 'active' | 'inactive';
  role: 'admin' | 'user' | 'guest';
  createTime: string;
  updateTime: string;
}

export const mockUsers: MockUser[] = [
  {
    id: '1',
    name: '张三',
    email: 'zhang<PERSON>@example.com',
    avatar: 'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png',
    phone: '13800138001',
    status: 'active',
    role: 'admin',
    createTime: '2024-01-01 10:00:00',
    updateTime: '2024-01-15 14:30:00'
  },
  {
    id: '2',
    name: '李四',
    email: '<EMAIL>',
    avatar: 'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png',
    phone: '13800138002',
    status: 'active',
    role: 'user',
    createTime: '2024-01-02 11:00:00',
    updateTime: '2024-01-16 15:30:00'
  },
  {
    id: '3',
    name: '王五',
    email: '<EMAIL>',
    avatar: 'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png',
    phone: '13800138003',
    status: 'inactive',
    role: 'user',
    createTime: '2024-01-03 12:00:00',
    updateTime: '2024-01-17 16:30:00'
  },
  {
    id: '4',
    name: '赵六',
    email: '<EMAIL>',
    avatar: 'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png',
    phone: '13800138004',
    status: 'active',
    role: 'guest',
    createTime: '2024-01-04 13:00:00',
    updateTime: '2024-01-18 17:30:00'
  },
  {
    id: '5',
    name: '孙七',
    email: '<EMAIL>',
    avatar: 'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png',
    phone: '13800138005',
    status: 'active',
    role: 'user',
    createTime: '2024-01-05 14:00:00',
    updateTime: '2024-01-19 18:30:00'
  }
];

// 当前登录用户
export const currentUser: API.CurrentUser = {
  name: '张三',
  avatar: 'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png',
  userid: '1',
  email: '<EMAIL>',
  signature: '海纳百川，有容乃大',
  title: '系统管理员',
  group: '管理组',
  tags: [
    { key: '0', label: '很有想法的' },
    { key: '1', label: '专注设计' },
    { key: '2', label: '辣~' },
    { key: '3', label: '大长腿' },
    { key: '4', label: '川妹子' },
    { key: '5', label: '海纳百川' }
  ],
  notifyCount: 12,
  unreadCount: 11,
  country: 'China',
  access: 'admin',
  geographic: {
    province: { label: '四川省', key: '510000' },
    city: { label: '成都市', key: '510100' }
  },
  address: '四川省成都市高新区天府大道',
  phone: '13800138001'
};
