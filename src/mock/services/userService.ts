// 用户相关的Mock服务
import { MockService, PaginationParams, createPaginatedResponse } from '../index';
import { mockUsers, currentUser, MockUser } from '../data/users';

export class UserMockService extends MockService {
  private users: MockUser[] = [...mockUsers];

  // 获取当前用户信息
  async getCurrentUser() {
    await this.delay();
    return this.success(currentUser);
  }

  // 获取用户列表
  async getUserList(params: PaginationParams = {}) {
    await this.delay();

    // 模拟搜索过滤
    let filteredUsers = [...this.users];

    if (params.name) {
      filteredUsers = filteredUsers.filter(user =>
        user.name.includes(params.name)
      );
    }

    if (params.email) {
      filteredUsers = filteredUsers.filter(user =>
        user.email.includes(params.email)
      );
    }

    if (params.status) {
      filteredUsers = filteredUsers.filter(user =>
        user.status === params.status
      );
    }

    const paginatedData = createPaginatedResponse(filteredUsers, params);
    return this.success(paginatedData);
  }

  // 创建用户
  async createUser(userData: Partial<MockUser>) {
    await this.delay();

    const newUser: MockUser = {
      id: String(Date.now()),
      name: userData.name || '',
      email: userData.email || '',
      avatar: userData.avatar || 'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png',
      phone: userData.phone || '',
      status: userData.status || 'active',
      role: userData.role || 'user',
      createTime: new Date().toISOString(),
      updateTime: new Date().toISOString()
    };

    this.users.push(newUser);
    return this.success(newUser, '用户创建成功');
  }

  // 更新用户
  async updateUser(id: string, userData: Partial<MockUser>) {
    await this.delay();

    const userIndex = this.users.findIndex(user => user.id === id);
    if (userIndex === -1) {
      return this.error('用户不存在', 404);
    }

    this.users[userIndex] = {
      ...this.users[userIndex],
      ...userData,
      updateTime: new Date().toISOString()
    };

    return this.success(this.users[userIndex], '用户更新成功');
  }

  // 删除用户
  async deleteUser(id: string) {
    await this.delay();

    const userIndex = this.users.findIndex(user => user.id === id);
    if (userIndex === -1) {
      return this.error('用户不存在', 404);
    }

    this.users.splice(userIndex, 1);
    return this.success({}, '用户删除成功');
  }

  // 登录
  async login(params: { username?: string; password: string; mobile?: string; type?: string; email?: string }) {
    await this.delay();

    console.log('Mock登录参数:', params);

    // 获取用户名，支持多种字段
    const inputUsername = params.username || params.email || params.mobile || '';

    // 模拟登录验证 - 支持多种用户名密码组合
    const validCredentials = [
      { username: 'admin', password: 'ant.design' },
      { username: 'admin', password: '123456' },
      { username: 'user', password: '123456' },
      // 支持邮箱登录
      { username: '<EMAIL>', password: 'ant.design' },
      { username: '<EMAIL>', password: '123456' }
    ];

    const isValid = validCredentials.some(cred =>
      (cred.username === inputUsername || cred.username === 'admin') &&
      cred.password === params.password
    );

    if (isValid) {
      const loginData = {
        token: 'mock-jwt-token-' + Date.now(),
        type: 'Bearer',
        currentAuthority: 'admin',
        user: currentUser
      };
      return this.success(loginData, '登录成功');
    }

    return this.error('用户名或密码错误', 401);
  }

  // 登出
  async logout() {
    await this.delay();
    return this.success({}, '登出成功');
  }

  // 刷新token
  async refreshToken() {
    await this.delay();
    const tokenData = {
      token: 'mock-jwt-token-refreshed-' + Date.now(),
      type: 'Bearer'
    };
    return this.success(tokenData, 'Token刷新成功');
  }
}

export const userMockService = new UserMockService();
