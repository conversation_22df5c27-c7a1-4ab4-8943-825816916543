// 规则相关的Mock服务
import { MockService, PaginationParams, createPaginatedResponse } from '../index';
import { mockRules, MockRule } from '../data/rules';

export class RuleMockService extends MockService {
  private rules: MockRule[] = [...mockRules];

  // 获取规则列表
  async getRuleList(params: PaginationParams = {}) {
    await this.delay();
    
    let filteredRules = [...this.rules];
    
    if (params.name) {
      filteredRules = filteredRules.filter(rule => 
        rule.name.includes(params.name)
      );
    }
    
    if (params.status) {
      filteredRules = filteredRules.filter(rule => 
        rule.status === params.status
      );
    }

    const paginatedData = createPaginatedResponse(filteredRules, params);
    return this.success(paginatedData);
  }

  // 添加规则
  async addRule(ruleData: Partial<MockRule>) {
    await this.delay();
    
    const newRule: MockRule = {
      key: Date.now(),
      disabled: false,
      href: ruleData.href || 'https://ant.design',
      avatar: ruleData.avatar || 'https://gw.alipayobjects.com/zos/rmsportal/eeHMaZBwmTvLdIwMfBpg.png',
      name: ruleData.name || '',
      owner: ruleData.owner || '',
      desc: ruleData.desc || '',
      callNo: ruleData.callNo || 0,
      status: ruleData.status || '已上线',
      updatedAt: new Date().toISOString().split('T')[0],
      createdAt: new Date().toISOString().split('T')[0],
      progress: ruleData.progress || 0,
    };

    this.rules.push(newRule);
    return this.success(newRule, '规则添加成功');
  }

  // 更新规则
  async updateRule(key: number, ruleData: Partial<MockRule>) {
    await this.delay();
    
    const ruleIndex = this.rules.findIndex(rule => rule.key === key);
    if (ruleIndex === -1) {
      return this.error('规则不存在', 404);
    }

    this.rules[ruleIndex] = {
      ...this.rules[ruleIndex],
      ...ruleData,
      updatedAt: new Date().toISOString().split('T')[0]
    };

    return this.success(this.rules[ruleIndex], '规则更新成功');
  }

  // 删除规则
  async deleteRule(key: number) {
    await this.delay();
    
    const ruleIndex = this.rules.findIndex(rule => rule.key === key);
    if (ruleIndex === -1) {
      return this.error('规则不存在', 404);
    }

    this.rules.splice(ruleIndex, 1);
    return this.success({}, '规则删除成功');
  }
}

export const ruleMockService = new RuleMockService();
