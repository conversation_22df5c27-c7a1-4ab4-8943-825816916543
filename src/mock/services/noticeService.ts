// 通知相关的Mock服务
import { MockService } from '../index';
import { mockNotices, MockNotice } from '../data/notices';

export class NoticeMockService extends MockService {
  private notices: MockNotice[] = [...mockNotices];

  // 获取通知列表
  async getNotices() {
    await this.delay();
    
    const groupedNotices = {
      data: this.notices,
      total: this.notices.length,
      unreadCount: this.notices.filter(notice => !notice.read).length
    };
    
    return this.success(groupedNotices);
  }

  // 标记通知为已读
  async markNoticeAsRead(id: string) {
    await this.delay();
    
    const noticeIndex = this.notices.findIndex(notice => notice.id === id);
    if (noticeIndex === -1) {
      return this.error('通知不存在', 404);
    }

    this.notices[noticeIndex].read = true;
    return this.success({}, '通知已标记为已读');
  }

  // 清空通知
  async clearNotices(type?: string) {
    await this.delay();
    
    if (type) {
      this.notices = this.notices.filter(notice => notice.type !== type);
    } else {
      this.notices = [];
    }
    
    return this.success({}, '通知已清空');
  }
}

export const noticeMockService = new NoticeMockService();
