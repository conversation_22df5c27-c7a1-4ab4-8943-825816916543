// 通用服务的Mock
import { MockService } from '../index';

export class CommonMockService extends MockService {
  
  // 获取当前时间
  async getDateTime() {
    await this.delay();
    const now = new Date();
    return this.success({
      timestamp: now.getTime(),
      datetime: now.toISOString(),
      formatted: now.toLocaleString('zh-CN')
    });
  }

  // 文件上传token
  async getUploadToken(params: any) {
    await this.delay();
    return this.success({
      token: 'mock-upload-token-' + Date.now(),
      expire: Date.now() + 3600000, // 1小时后过期
      domain: 'https://mock-cdn.example.com'
    });
  }

  // 根据ID获取文件URL列表
  async getFileUrlListByIds(params: { ids: string[] }) {
    await this.delay();
    const fileUrls = params.ids.map(id => ({
      id,
      url: `https://mock-cdn.example.com/files/${id}.jpg`,
      name: `file_${id}.jpg`,
      size: Math.floor(Math.random() * 1000000) + 100000
    }));
    
    return this.success(fileUrls);
  }

  // 文件上传
  async uploadFile(file: File, path: string) {
    await this.delay(1000); // 模拟上传时间
    
    const fileId = 'file_' + Date.now();
    const fileUrl = `https://mock-cdn.example.com/${path}/${fileId}`;
    
    return this.success({
      id: fileId,
      url: fileUrl,
      name: file.name,
      size: file.size,
      type: file.type
    }, '文件上传成功');
  }
}

export const commonMockService = new CommonMockService();
