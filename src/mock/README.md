# Mock数据系统使用说明

## 概述

本项目已集成完整的本地模拟数据系统，可以在没有后端服务的情况下正常运行和开发。

## 功能特性

- ✅ 完整的用户管理模拟数据
- ✅ 规则列表模拟数据
- ✅ 通知系统模拟数据
- ✅ 文件上传模拟功能
- ✅ 登录/登出模拟
- ✅ 分页查询支持
- ✅ 实时开关控制
- ✅ 可视化状态指示器

## 目录结构

```
src/mock/
├── index.ts              # Mock基础类和工具函数
├── mockManager.ts        # Mock管理器，统一管理所有服务
├── data/                 # 模拟数据文件
│   ├── users.ts         # 用户数据
│   ├── rules.ts         # 规则数据
│   └── notices.ts       # 通知数据
└── services/            # Mock服务类
    ├── userService.ts   # 用户相关服务
    ├── ruleService.ts   # 规则相关服务
    ├── noticeService.ts # 通知相关服务
    └── commonService.ts # 通用服务
```

## 配置文件

### src/config/mock-config.ts
控制Mock系统的全局配置：
- `enabled`: 是否启用Mock（开发环境默认启用）
- `delay`: 模拟网络延迟时间
- `showLogs`: 是否显示Mock日志

### src/config/request-config.ts
已更新为支持Mock模式，当启用Mock时会自动切换到本地数据。

## 使用方法

### 1. 启用/禁用Mock

**方法一：通过配置文件**
```typescript
// src/config/mock-config.ts
export const mockConfig = {
  enabled: true, // 设置为true启用Mock
  // ...
};
```

**方法二：通过代码控制**
```typescript
import { setMockEnabled } from '@/config/mock-config';

// 启用Mock
setMockEnabled(true);

// 禁用Mock
setMockEnabled(false);
```

**方法三：通过页面右上角的Mock指示器**
页面右上角会显示Mock状态指示器，可以直接点击开关切换。

### 2. 测试Mock功能

访问 `/mock-test` 页面可以测试所有Mock功能是否正常工作。

### 3. 添加新的Mock数据

**步骤1：创建数据文件**
```typescript
// src/mock/data/newData.ts
export interface MockNewData {
  id: string;
  name: string;
  // ...
}

export const mockNewData: MockNewData[] = [
  // 你的模拟数据
];
```

**步骤2：创建服务类**
```typescript
// src/mock/services/newService.ts
import { MockService } from '../index';
import { mockNewData } from '../data/newData';

export class NewMockService extends MockService {
  async getNewData() {
    await this.delay();
    return this.success(mockNewData);
  }
}

export const newMockService = new NewMockService();
```

**步骤3：更新API服务**
```typescript
// src/services/your-api.ts
import { requestConfig } from '@/config/request-config';
import { newMockService } from '@/mock/services/newService';

export async function getNewData() {
  if (requestConfig.useMock) {
    return newMockService.getNewData();
  }
  // 原有的API调用
  return request('/api/new-data', { method: 'GET' });
}
```

## 默认登录信息

- 用户名：`admin`
- 密码：`ant.design`

## 注意事项

1. Mock数据仅在内存中存储，页面刷新后会重置
2. 开发环境默认启用Mock，生产环境默认禁用
3. 切换Mock状态后建议刷新页面以确保生效
4. 所有Mock服务都包含模拟的网络延迟（默认300ms）

## 故障排除

### Mock数据不生效
1. 检查 `src/config/mock-config.ts` 中的 `enabled` 配置
2. 确认 `src/config/request-config.ts` 中的 `useMock` 配置
3. 查看浏览器控制台是否有错误信息
4. 尝试刷新页面

### API调用仍然请求真实接口
1. 确认对应的API服务文件已添加Mock判断逻辑
2. 检查import路径是否正确
3. 确认Mock服务类已正确导出

如有问题，请查看浏览器控制台的详细错误信息。
