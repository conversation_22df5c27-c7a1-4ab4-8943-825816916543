// Mock数据管理中心
import { ResponseStructure } from '@/utils/http-handle';

// 模拟延迟
const mockDelay = (ms: number = 300) => new Promise(resolve => setTimeout(resolve, ms));

// 统一的响应格式
export const createMockResponse = <T>(data: T, message: string = 'success'): ResponseStructure => ({
  code: 0,
  data,
  message,
  ok: true,
  msg: message
});

// 错误响应
export const createErrorResponse = (message: string, code: number = 1): ResponseStructure => ({
  code,
  data: {},
  message,
  ok: false,
  msg: message
});

// Mock服务基类
export class MockService {
  protected async delay(ms: number = 300) {
    await mockDelay(ms);
  }

  protected success<T>(data: T, message: string = 'success') {
    return createMockResponse(data, message);
  }

  protected error(message: string, code: number = 1) {
    return createErrorResponse(message, code);
  }
}

// 分页参数接口
export interface PaginationParams {
  current?: number;
  pageSize?: number;
  [key: string]: any;
}

// 分页响应接口
export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  current: number;
  pageSize: number;
}

// 创建分页响应
export const createPaginatedResponse = <T>(
  allData: T[],
  params: PaginationParams
): PaginatedResponse<T> => {
  const current = params.current || 1;
  const pageSize = params.pageSize || 10;
  const start = (current - 1) * pageSize;
  const end = start + pageSize;
  
  return {
    data: allData.slice(start, end),
    total: allData.length,
    current,
    pageSize
  };
};
